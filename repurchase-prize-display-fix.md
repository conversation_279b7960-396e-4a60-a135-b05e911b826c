# 复购奖品显示优化修复

## 修复需求
奖品名称和奖品类型没有的时候展示"-"或者空，不要显示"谢谢参与"

## 修复内容

### 1. 修复初始数据结构
**修改前：**
```javascript
const initPrizeItem = {
    lotteryName: '谢谢参与',
    lotteryType: PrizeTypeEnum.THANKS.value,
    // ...
};
```

**修改后：**
```javascript
const initPrizeItem = {
    lotteryName: '', // 空字符串而不是"谢谢参与"
    lotteryType: '', // 空字符串而不是默认类型
    // ...
};
```

### 2. 修复编辑条件判断
**修改前：**
```javascript
if (isNewPrize && (currentRecord.lotteryName === '' || currentRecord.lotteryName === '谢谢参与')) {
```

**修改后：**
```javascript
if (isNewPrize && (!currentRecord.lotteryName || currentRecord.lotteryName === '')) {
```

### 3. 修复表格显示逻辑
**奖品类型列修改：**
```javascript
// 修改前
cell={(value) => getPrizeTypeLabel(value)}

// 修改后
cell={(value) => value ? getPrizeTypeLabel(value) : '-'}
```

**单位数量列修改：**
```javascript
// 修改前
return record.lotteryValue;

// 修改后  
return record.lotteryValue || '-';
```

## 修复效果

### ✅ 显示优化
1. **奖品名称为空时**：显示 "-" 而不是 "谢谢参与"
2. **奖品类型为空时**：显示 "-" 而不是类型标签
3. **单位数量为空时**：显示 "-" 而不是空白

### ✅ 逻辑优化
1. **初始状态更清晰**：新建奖品时不会预设"谢谢参与"
2. **编辑条件更准确**：只有真正为空的奖品才会触发选择组件
3. **数据一致性**：所有空值统一显示为"-"

## 用户体验改进
- 🎯 **更清晰的空状态显示**：用户能明确知道哪些字段还未设置
- 🎯 **更直观的编辑入口**：只有真正需要设置的奖品才会弹出选择组件
- 🎯 **统一的UI风格**：所有空值都用"-"表示，保持界面一致性

## 测试建议
1. 新建活动时，复购奖品列表应显示空的奖品项（奖品名称和类型显示"-"）
2. 点击编辑按钮，应该弹出奖品选择组件
3. 编辑完成后，奖品信息应正确显示
4. 清空奖品信息后，应显示"-"而不是"谢谢参与"
