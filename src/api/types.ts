/** Activity10002ActivityDataRequest */
export interface Activity10002ActivityDataRequest {
  /** 活动基本数据 */
  base: ActivityInfoRequest;
  /** 任务设置 */
  chance: Activity10002ChanceRequest;
  /** 抽奖限制 */
  limit: Activity10002LimitRequest;
  /** 奖品数据 */
  prize: DzLotteryConfig[];
  /** 推荐商品 */
  recommendGoods: ProductInfoResponse[];
  /** 活动规则 */
  rule: string;
  /** 分享设置 */
  share: ActivityShareRequest;
  /** 活动门槛 */
  threshold: ActivityThresholdRequest;
}

/** Activity10002AddLotteryNumRequest */
export interface Activity10002AddLotteryNumRequest {
  /** @format int32 */
  addNum?: number;
  /** @format string */
  id?: string;
  lotteryValue?: string;
}

/** Activity10002ChanceRequest */
export interface Activity10002ChanceRequest {
  /**
   * 1首次访问活动赠送2每天赠送
   * @format int32
   */
  chanceType?: number;
  /**
   * 免费赠送次数
   * @format int32
   */
  freeChance?: number;
  /**
   * 是否设置任务 1 设置 2 不设置
   * @format int32
   */
  taskChance?: number;
  /** 任务列表 */
  taskList?: object[];
}

/** Activity10002CreateOrUpdateRequest */
export interface Activity10002CreateOrUpdateRequest {
  /** 活动数据 */
  activityData: Activity10002ActivityDataRequest;
  /**
   * 装修数据
   * @example {"background-color":"#F00"}
   */
  decoData: string;
  originalActivityData?: string;
}

/** Activity10002CreateOrUpdateResponse */
export interface Activity10002CreateOrUpdateResponse {
  /**
   * 活动id
   * @format string
   */
  activityId?: string;
  /** 活动链接 */
  activityUrl?: string;
}

/** Activity10002DrawChanceRecordRequest */
export interface Activity10002DrawChanceRecordRequest {
  /** 活动id */
  activityId?: string;
  /**
   * 结束时间
   * @format date-time
   */
  endTime?: string;
  /** 用户昵称 */
  nick?: string;
  /** 用户pin */
  openId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 开始时间
   * @format date-time
   */
  startTime?: string;
  /**
   * 任务类型
   * @format int32
   */
  taskType?: number;
}

/** Activity10002DrawChanceRecordResponse */
export interface Activity10002DrawChanceRecordResponse {
  /**
   * 发放抽奖次数
   * @format int32
   */
  chanceNum?: number;
  /**
   * 发放时间
   * @format date-time
   */
  createTime?: string;
  /** 任务明细 */
  details?: string;
  /** 用户昵称 */
  nickName?: string;
  /** 备注 */
  notes?: string;
  /** openId */
  openId?: string;
  /** @format int32 */
  taskType?: number;
  /** 完成任务类型 */
  taskTypeStr?: string;
}

/** Activity10002LimitRequest */
export interface Activity10002LimitRequest {
  /**
   * 奖品全部发完可继续抽奖1开启2关闭
   * @format int32
   */
  continueDraw?: number;
  /**
   * 每人每天中奖次数
   * @format int32
   */
  limitCount?: number;
  /**
   * 每人每天中奖限制1限制2不限制
   * @format int32
   */
  limitType?: number;
  /**
   * 开启获奖名单1开启2不开启
   * @format int32
   */
  openAwardList?: number;
  /**
   * 每人累计中奖次数
   * @format int32
   */
  totalLimitCount?: number;
  /**
   * 累计中奖限制1限制2不限制
   * @format int32
   */
  totalLimitType?: number;
}

/** Activity10002PhysicalPrizeInfoRequest */
export interface Activity10002PhysicalPrizeInfoRequest {
  /** 活动id */
  activityId?: string;
  /**
   * 结束时间
   * @format date-time
   */
  endTime?: string;
  /** 用户昵称 */
  nick?: string;
  /** 用户pin */
  openId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 开始时间
   * @format date-time
   */
  startTime?: string;
}

/** Activity10002PhysicalPrizeInfoResponse */
export interface Activity10002PhysicalPrizeInfoResponse {
  /** 详细地址 */
  address?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /**
   * 获奖时间
   * @format date-time
   */
  createTime?: string;
  /** 收件人手机号 */
  mobile?: string;
  /** 用户昵称 */
  nickName?: string;
  /** openId */
  openId?: string;
  /** 奖品名称 */
  prizeName?: string;
  /** 省 */
  province?: string;
  /** 收件人 */
  realName?: string;
}

/** Activity10002PrizeInfoRequest */
export interface Activity10002PrizeInfoRequest {
  /** 活动id */
  activityId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
}

/** Activity10002PrizeInfoResponse */
export interface Activity10002PrizeInfoResponse {
  /**
   * 奖品id
   * @format string
   */
  id?: string;
  /** 奖品名称 */
  lotteryName?: string;
  /** @format int32 */
  lotteryType?: number;
  /** 奖品类型 */
  lotteryTypeStr?: string;
  /**
   * 可抽取的奖品总数
   * @format int32
   */
  prizeNum?: number;
  /**
   * 剩余的资产中心库存数量
   * @format int32
   */
  remainLotteryPrizeNum?: number;
  /**
   * 剩余的奖品数量
   * @format int32
   */
  remainPrizeNum?: number;
  /**
   * 已抽取的奖品数量
   * @format int32
   */
  sendPrizeNum?: number;
}

/** Activity10002PrizeLogRequest */
export interface Activity10002PrizeLogRequest {
  /**
   * 奖品id
   * @format string
   */
  id?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
}

/** Activity10002PrizeLogResponse */
export interface Activity10002PrizeLogResponse {
  /**
   * 追加数量
   * @format int32
   */
  addNum?: number;
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /**
   * 追加后发放总量
   * @format int32
   */
  newNum?: number;
  /**
   * 追加前发放总量
   * @format int32
   */
  oldNum?: number;
  /** 操作人 */
  operator?: string;
}

/** Activity10002PrizeRecordRequest */
export interface Activity10002PrizeRecordRequest {
  /** 活动id */
  activityId?: string;
  /**
   * 中奖状态 0 全部 1 已中奖 2 未中奖
   * @format int32
   */
  drawStatus?: number;
  /**
   * 结束时间
   * @format date-time
   */
  endTime?: string;
  /** 用户昵称 */
  nick?: string;
  /** 用户pin */
  openId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 领取状态 0 全部 1 成功 2 失败
   * @format int32
   */
  receiveStatus?: number;
  /**
   * 开始时间
   * @format date-time
   */
  startTime?: string;
}

/** Activity10002PrizeRecordResponse */
export interface Activity10002PrizeRecordResponse {
  /**
   * 抽奖时间
   * @format date-time
   */
  createTime?: string;
  /**
   * 中奖记录表id
   * @format string
   */
  drawRecordId?: string;
  /** 未中奖原因 */
  missReason?: string;
  /** 昵称 */
  nickName?: string;
  /** openId */
  openId?: string;
  /** 奖品名称 */
  prizeName?: string;
  /** @format int32 */
  prizeType?: number;
  /** 奖品类型 */
  prizeTypeStr?: string;
  /** @format int32 */
  sendStatus?: number;
  /** 奖品发放状态 1 发放成功 2 发放失败 */
  sendStatusStr?: string;
  /** @format int32 */
  status?: number;
  /** 状态0-未中奖1-已中奖 */
  statusStr?: string;
}

/** Activity10201ActivityData */
export interface Activity10201ActivityData {
  /** 活动基本数据 */
  base: ActivityInfoRequest;
  /** 订单限制 */
  order?: Activity10201OrderLimitRequest;
  /** 系列列表 */
  prizeAndSku?: Activity10201SeriesLimitRequest;
  /** 推荐商品 */
  recommendGoods: ProductInfoResponse[];
  /** 活动规则 */
  rule: string;
  /** 分享设置 */
  share: ActivityShareRequest;
  /** 活动兑换次数限制 */
  step?: Activity10201StepLimitRequest;
  /** 活动门槛 */
  threshold: ActivityThresholdRequest;
}

/** Activity10201ActivitySeriesData */
export interface Activity10201ActivitySeriesData {
  /**
   * 领取次数
   * @format int32
   */
  perReceiveCount?: number;
  /**
   * 相同奖品是否可以重复领取1可以重复2不可以
   * @format int32
   */
  prizeReceiveLimit?: number;
  /** 系列名 */
  seriesName?: string;
  /** 图片 */
  seriesPic?: string;
  /** 系列商品 */
  seriesSkuList?: Activity10201ActivitySeriesSkuData[];
  /** 跳转链接 */
  seriesUrl?: string;
  /**
   * 顺序号
   * @format int32
   */
  sortId?: number;
  /** 阶梯 */
  stepList?: Activity10201StepRequest[];
  /**
   * 系列内总兑换次数
   * @format int32
   */
  totalReceiveCount?: number;
}

/** Activity10201ActivitySeriesSkuData */
export interface Activity10201ActivitySeriesSkuData {
  /** 品线 */
  productLine?: string;
  /**
   * 商品id
   * @format string
   */
  skuId?: string;
  /**
   * 排序
   * @format int32
   */
  sort?: number;
  /** 商品主id */
  spuId?: string;
  /**
   * 罐数
   * @format int32
   */
  tankNum?: number;
}

/** Activity10201CreateOrUpdateRequest */
export interface Activity10201CreateOrUpdateRequest {
  /** 活动数据 */
  activityData: Activity10201ActivityData;
  /**
   * 装修数据
   * @example {"background-color":"#F00"}
   */
  decoData: string;
  originalActivityData?: string;
}

/** Activity10201CreateOrUpdateResponse */
export interface Activity10201CreateOrUpdateResponse {
  /**
   * 活动id
   * @format string
   */
  activityId?: string;
  /** 活动链接 */
  activityUrl?: string;
}

/** Activity10201OrderLimitRequest */
export interface Activity10201OrderLimitRequest {
  /**
   * 订单延迟时间
   * @format int32
   */
  awardDays?: number;
  /**
   * 是否延迟订单
   * @format int32
   */
  isAwardDays?: number;
  /**
   * 是否限制订单
   * @format int32
   */
  limitOrder?: number;
  /**
   * 订单结束时间
   * @format date-time
   */
  orderEndTime?: string;
  /**
   * 订单开始时间
   * @format date-time
   */
  orderStartTime?: string;
}

/** Activity10201PrizeInfoRequest */
export interface Activity10201PrizeInfoRequest {
  /** 活动id */
  activityId?: string;
}

/** Activity10201PrizeInfoResponse */
export interface Activity10201PrizeInfoResponse {
  /**
   * 奖品id
   * @format string
   */
  prizeId?: string;
  /**
   * 顺序
   * @format int32
   */
  prizeSort?: number;
  /**
   * 阶梯名称
   * @format int32
   */
  stepSort?: number;
}

/** Activity10201SeriesImportResponse */
export interface Activity10201SeriesImportResponse {
  /** 系列名 */
  seriesName?: string;
  /** 系列sku列表 */
  seriesSkuInfoList?: Activity10201SeriesSkuImportResponse[];
}

/** Activity10201SeriesLimitRequest */
export interface Activity10201SeriesLimitRequest {
  /** 系列列表 */
  seriesList?: Activity10201ActivitySeriesData[];
}

/** Activity10201SeriesSkuImportResponse */
export interface Activity10201SeriesSkuImportResponse {
  /** 品线 */
  productLine?: string;
  /**
   * skuId
   * @format string
   */
  skuId?: string;
  /**
   * 排序
   * @format int32
   */
  sort?: number;
  /** spuId */
  spuId?: string;
  /**
   * 罐数
   * @format int32
   */
  tankNum?: number;
}

/** Activity10201StepLimitRequest */
export interface Activity10201StepLimitRequest {
  /**
   * 是否限制活动内总兑换次数 0 不限制 1 限制
   * @format int32
   */
  actLimitActTotalReceiveCount?: number;
  /**
   * 活动内总兑换次数
   * @format int32
   */
  actTotalReceiveCount?: number;
}

/** Activity10201StepRequest */
export interface Activity10201StepRequest {
  /** 系列资产 */
  prizeList?: DzLotteryConfig[];
  /**
   * 系列ID
   * @format string
   */
  seriesId?: string;
  /**
   * 顺序号
   * @format int32
   */
  sortId?: number;
  /** 阶梯名称 */
  stepName?: string;
  /**
   * 罐数
   * @format int32
   */
  tankNum?: number;
}

/** Activity10201UserPotRecordPageResponse */
export interface Activity10201UserPotRecordPageResponse {
  /**
   * 参与时间
   * @format date-time
   */
  createTime?: string;
  /** 用户pin */
  encryptPin?: string;
  /**
   * 剩余罐数
   * @format int32
   */
  leftPot?: number;
  /** 用户昵称 */
  nickName?: string;
  /** 系列名称 */
  seriesId?: string;
  /**
   * 总罐数
   * @format int32
   */
  totalPot?: number;
  /**
   * 使用罐数
   * @format int32
   */
  usePot?: number;
}

/** Activity10201UserPrizeRecordPageRequest */
export interface Activity10201UserPrizeRecordPageRequest {
  /**
   * activityId
   * @format string
   */
  activityId?: string;
  /** 领取时间 */
  dateRange?: string[];
  /** 描述 */
  description?: string;
  /** 人群包名 */
  packName?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** pin */
  pin?: string;
  /**
   * 领取状态 -1-全部 0-失败 1-成功
   * @format int32
   */
  status?: number;
}

/** Activity10201UserWinningLogRequest */
export interface Activity10201UserWinningLogRequest {
  /**
   * activityId
   * @format string
   */
  activityId?: string;
  /** 领取时间 */
  dateRange?: string[];
  /** 描述 */
  description?: string;
  /** 人群包名 */
  packName?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** pin */
  pin?: string;
  /** 领取状态 ''-全部 0-失败 1-成功 */
  status?: string;
}

/** Activity10201UserWinningLogResponse */
export interface Activity10201UserWinningLogResponse {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /** 用户身份 */
  encryptPin?: string;
  /** 昵称 */
  nickName?: string;
  /** 奖品名称 */
  prizeName?: string;
  /** 资产类型 */
  prizeType?: string;
  /** 原因 */
  reason?: string;
  /** 系列名称 */
  seriesName?: string;
  /** 状态 0-失败1-成功 */
  status?: string;
  /** 阶梯名称 */
  stepName?: string;
}

/** Activity10202ActivityData */
export interface Activity10202ActivityData {
  /** 活动基本数据 */
  base: ActivityInfoRequest;
  /** 小罐奖品sku */
  demoSkuList?: Activity10202DemoSkuImportRequest[];
  /** 订单限制 */
  order?: Activity10202OrderRequest;
  /** 推荐商品 */
  recommendGoods: ProductInfoResponse[];
  /** 复购订单限制 */
  repurchase?: Activity10202RepurchaseRequest;
  /** 活动规则 */
  rule: string;
  /** 分享设置 */
  share: ActivityShareRequest;
  /** 活动门槛 */
  threshold: ActivityThresholdRequest;
}

/** Activity10202CreateOrUpdateRequest */
export interface Activity10202CreateOrUpdateRequest {
  /** 活动数据 */
  activityData: Activity10202ActivityData;
  /**
   * 装修数据
   * @example {"background-color":"#F00"}
   */
  decoData: string;
  originalActivityData?: string;
}

/** Activity10202CreateOrUpdateResponse */
export interface Activity10202CreateOrUpdateResponse {
  /**
   * 活动id
   * @format string
   */
  activityId?: string;
  /** 活动链接 */
  activityUrl?: string;
}

/** Activity10202DemoSkuImportRequest */
export interface Activity10202DemoSkuImportRequest {
  /**
   * 是否每日限制0-不限制1-限制
   * @format int32
   */
  dayLimit?: number;
  /**
   * 每日限制数量
   * @format int32
   */
  dayLimitCount?: number;
  /** 品线 */
  goodsLine?: string;
  /** 奖品列表 */
  prizeList?: DzLotteryConfig[];
  /** 奖品主图 */
  prizeMainImg?: string;
  /** 奖品主名称 */
  prizeMainName?: string;
  /**
   * 段数
   * @format int32
   */
  sectionSort?: number;
  /**
   * skuId
   * @format string
   */
  skuId?: string;
  /** sku图片 */
  skuMainPicture?: string;
  /** sku名称 */
  skuName?: string;
}

/** Activity10202OrderRequest */
export interface Activity10202OrderRequest {
  /**
   * 前置订单延迟发奖天数
   * @format int32
   */
  awardDaysBefore?: number;
  /** 前置正装sku */
  beforeSkuList?: Activity10202SkuImportRequest[];
  /**
   * 前置订单前推天数
   * @format int32
   */
  days?: number;
  /**
   * 前置订单是否延迟发奖 0-不延迟 1-延迟
   * @format int32
   */
  isDelayedDisttributionBefore?: number;
}

/** Activity10202RepurchaseRequest */
export interface Activity10202RepurchaseRequest {
  /**
   * 延迟天数
   * @format int32
   */
  awardDays?: number;
  /**
   * 奖品延迟发放 0-不延迟 1-延迟
   * @format int32
   */
  isDelayedDisttribution?: number;
  /** 复购奖品列表 */
  repurchasePrizeList?: DzLotteryConfig[];
  /** 复购sku */
  repurchaseSkuList?: Activity10202SkuImportRequest[];
}

/** Activity10202SectionRequest */
export interface Activity10202SectionRequest {
  /**
   * activityId
   * @format string
   */
  activityId?: string;
}

/** Activity10202SectionResponse */
export interface Activity10202SectionResponse {
  /** 品线名称 */
  goodsLine?: Record<string, string>;
  /** 复购订单号 */
  sectionInfo?: Record<string, number>;
}

/** Activity10202SkuAfterTemplate */
export interface Activity10202SkuAfterTemplate {
  /** 品线 */
  goodsLine?: string;
  /**
   * 段数
   * @format int32
   */
  sectionNum?: number;
  /**
   * skuId
   * @format string
   */
  skuId?: string;
  /** sku图片 */
  skuMainPicture?: string;
  /** sku名称 */
  skuName?: string;
  /**
   * spuId
   * @format string
   */
  spuId?: string;
}

/** Activity10202SkuBeforeTemplate */
export interface Activity10202SkuBeforeTemplate {
  /** 品线 */
  goodsLine?: string;
  /**
   * 段数
   * @format int32
   */
  sectionNum?: number;
  /**
   * skuId
   * @format string
   */
  skuId?: string;
  /** 默认兜底图 */
  skuMainPicture?: string;
  /** sku名称 */
  skuName?: string;
  /**
   * spuId
   * @format string
   */
  spuId?: string;
}

/** Activity10202SkuDemoResponse */
export interface Activity10202SkuDemoResponse {
  /**
   * 段数
   * @format int32
   */
  sectionNum?: number;
  /** 商品信息 */
  skuInfo?: Activity10202SkuDemoTemplate[];
}

/** Activity10202SkuDemoTemplate */
export interface Activity10202SkuDemoTemplate {
  /**
   * 每日限额
   * @format int32
   */
  dayLimitCount?: number;
  /** 品线 */
  goodsLine?: string;
  /** 奖品名 */
  prizeMainName?: string;
  /**
   * 段数
   * @format int32
   */
  sectionNum?: number;
  /**
   * skuId
   * @format string
   */
  skuId?: string;
  /** sku图 */
  skuMainPicture?: string;
  /** sku名称 */
  skuName?: string;
}

/** Activity10202SkuImportRequest */
export interface Activity10202SkuImportRequest {
  /** 品线 */
  goodsLine?: string;
  /**
   * 段数
   * @format int32
   */
  sectionNum?: number;
  /**
   * skuId
   * @format string
   */
  skuId?: string;
  /** 默认兜底图片 */
  skuMainPicture?: string;
  /** sku名称 */
  skuName?: string;
}

/** Activity10202UserAfterWinningLogRequest */
export interface Activity10202UserAfterWinningLogRequest {
  /**
   * activityId
   * @format string
   */
  activityId?: string;
  /** 领取时间 */
  dateRange?: string[];
  /** 描述 */
  description?: string;
  /** 人群包名 */
  packName?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** pin */
  pin?: string;
  /** 复购奖品名称 */
  prizeName?: string;
}

/** Activity10202UserAfterWinningLogResponse */
export interface Activity10202UserAfterWinningLogResponse {
  /**
   * 复购奖品领取时间
   * @format date-time
   */
  createTime?: string;
  /** 用户身份 */
  encryptPin?: string;
  /** 复购订单号 */
  orderId?: string;
  /**
   * 复购订单下单时间
   * @format date-time
   */
  orderStartTime?: string;
  /** 复购奖品名称 */
  prizeName?: string;
  /** 复购订单sku */
  skuIds?: string;
}

/** Activity10202UserTotalWinningLogRequest */
export interface Activity10202UserTotalWinningLogRequest {
  /** pin */
  pin?: string;
}

/** Activity10202UserTotalWinningLogResponse */
export interface Activity10202UserTotalWinningLogResponse {
  /** 领奖活动id */
  activityId?: string;
  /** 活动奖品类型 */
  activityPrizeType?: string;
  /**
   * 领奖时间
   * @format date-time
   */
  createTime?: string;
  /** 用户身份 */
  encryptPin?: string;
  /** 奖品名称 */
  prizeName?: string;
  /** 活动奖品段数 */
  sectionNum?: string;
  /** 领奖店铺id */
  shopId?: string;
}

/** Activity10202UserWinningLogRequest */
export interface Activity10202UserWinningLogRequest {
  /**
   * activityId
   * @format string
   */
  activityId?: string;
  /** 领取时间 */
  dateRange?: string[];
  /** 描述 */
  description?: string;
  /** 品线 */
  goodsLine?: string;
  /** 是否完成复购 0-未使用 1-使用 */
  hasRepurchased?: string;
  /** 昵称 */
  nickName?: string;
  /** 人群包名 */
  packName?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** pin */
  pin?: string;
  /** 奖品名称 */
  prizeName?: string;
  /**
   * 阶段
   * @format int32
   */
  sectionNum?: number;
}

/** Activity10202UserWinningLogResponse */
export interface Activity10202UserWinningLogResponse {
  /**
   * 奖品领取时间
   * @format date-time
   */
  createTime?: string;
  /** 用户身份 */
  encryptPin?: string;
  /** 订单品线 */
  goodsLine?: string;
  /** 是否完成复购 0-未使用 1-使用 */
  hasRepurchased?: string;
  /** 昵称 */
  nickName?: string;
  /**
   * 前置订单完成时间
   * @format date-time
   */
  orderEndTime?: string;
  /** 前置订单号 */
  orderId?: string;
  /** 奖品名称 */
  prizeName?: string;
  /** 资产类型 */
  prizeType?: string;
  /** 订单段位 */
  sectionNum?: string;
}

/** ActivityData */
export interface ActivityData {
  /** @format string */
  activityOrderNum?: string;
  activityOrderNums?: string;
  activityOrderPayment?: string;
  aus?: string;
  divOrder?: string;
  freq?: string;
  /** @format string */
  joinMemberUv?: string;
  /** @format string */
  joinUv?: string;
  /** @format string */
  newUserOrderNum?: string;
  /** @format string */
  oldUserOrderNum?: string;
  opdate?: string;
  /** @format string */
  orderNum?: string;
  /** @format int32 */
  pv?: number;
  spending?: string;
  statisticsTime?: string;
  /** @format int32 */
  uv?: number;
}

/** ActivityDataBasicRequest */
export interface ActivityDataBasicRequest {
  /** 活动类型 */
  actType?: string;
  /** 活动id */
  activityId?: string;
  /**
   * 结束时间
   * @format date-time
   */
  endTime?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** 查询类型 */
  queryType?: string;
  /**
   * 开始时间
   * @format date-time
   */
  startTime?: string;
}

/** ActivityDataBasicTotalResponse */
export interface ActivityDataBasicTotalResponse {
  /** 数据描述 */
  desc?: string;
  /** 数据key */
  key?: string;
  /** 数据表头名 */
  title?: string;
  /** 数据值 */
  value?: string;
}

/** ActivityDataByDayPage */
export interface ActivityDataByDayPage {
  /** @format int32 */
  current?: number;
  /** @format int32 */
  pages?: number;
  records?: string[][];
  /** @format int32 */
  size?: number;
  titles?: string[];
  /** @format int32 */
  total?: number;
}

/** ActivityDecorationDatRequest */
export interface ActivityDecorationDatRequest {
  /**
   * 活动ID
   * @format string
   */
  activityId?: string;
}

/** ActivityDecorationDatResponse */
export interface ActivityDecorationDatResponse {
  /** 活动数据 */
  activityData?: string;
  /** 装修数据 */
  decorationData?: string;
}

/** ActivityInfoRequest */
export interface ActivityInfoRequest {
  /**
   * 活动id
   * @format string
   */
  activityId?: string;
  /**
   * 活动名称
   * @example "测试活动名称"
   */
  activityName?: string;
  /** 活动链接 */
  activityUrl?: string;
  /**
   * 活动结束时间
   * @format date-time
   */
  endTime?: string;
  /** 小程序图片 */
  mpImg?: string;
  /**
   * 活动规则
   * @example "活动规则"
   */
  rule?: string;
  /** 分享内容 */
  shareContent?: string;
  /** 分享标题 */
  shareTitle?: string;
  /**
   * 活动开始时间
   * @format date-time
   */
  startTime?: string;
  /**
   * 奖品扣减方式 1发放时扣减 2预约时扣减
   * @format int32
   * @example 1
   */
  subtractionType?: number;
  /**
   * 活动模板号
   * @format int32
   * @example 301
   */
  templateCode?: number;
  /**
   * 类型 1 大转盘 2 抽盲盒
   * @format int32
   */
  wheelType?: number;
}

/** ActivityShareRequest */
export interface ActivityShareRequest {
  /** 小程序图片 */
  mpImg?: string;
  /** 分享内容 */
  shareContent?: string;
  /** 分享标题 */
  shareTitle?: string;
}

/** ActivityShopGradeResponse */
export interface ActivityShopGradeResponse {
  /**
   * 等级
   * @format int32
   */
  grade?: number;
  /** 等级名称 */
  gradeName?: string;
}

/** ActivityTemplateRequest */
export interface ActivityTemplateRequest {
  /** 活动类型 */
  actType?: string;
}

/** ActivityTemplateResponse */
export interface ActivityTemplateResponse {
  /**
   * 活动类型编号
   * @format int32
   */
  activityType?: number;
  /**
   * 模板号:手动录入，101-199为系统内置模板，201-299为定制模板
   * @format int32
   */
  code?: number;
  /** 模板配置 */
  configValue?: string;
  /** 模板配图 */
  cover?: string;
  /**
   * 店铺ID
   * @format string
   */
  shopId?: string;
  /**
   * 模板作用域：1、系统内置模板；2、店铺定制模板、3、用户自定义模板（用户手动转存的）
   * @format int32
   */
  templateType?: number;
  /** 模板名称 */
  title?: string;
}

/** ActivityThresholdRequest */
export interface ActivityThresholdRequest {
  /**
   * 是否是会员 0否 1是
   * @format int32
   */
  isMember?: number;
  /** 门槛信息 */
  thresholdInfo?: ThresholdInfo;
  /**
   * 1全部2指定人群3选择CDP人群4排除CDP人群
   * @format int32
   */
  thresholdType?: number;
}

/** ActivityThresholdResponse */
export interface ActivityThresholdResponse {
  thresholdNum?: string[];
}

/** ActivityTypeResponse */
export interface ActivityTypeResponse {
  /** 类型汉字 */
  label?: string;
  /**
   * 类型编号
   * @format int32
   */
  value?: number;
}

/** AddLotteryNumRequest */
export interface AddLotteryNumRequest {
  /** @format int32 */
  addNum?: number;
  /** @format string */
  id?: string;
  lotteryValue?: string;
}

/** AlibabaBenefitCenterVO */
export interface AlibabaBenefitCenterVO {
  /** 金额 */
  amount?: string;
  /**
   * 奖池类型,0:抽奖,1:发奖
   * @format int32
   */
  awardType?: number;
  /** 权益内容 */
  benefitName?: string;
  /**
   * 权益状态
   * @format int32
   */
  benefitStatus?: number;
  /**
   * 已绑定活动数量-冻结库存
   * @format int32
   */
  bindActivityNum?: number;
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /** 权益状态 */
  deletedStatus?: boolean;
  /** 奖池唯一id */
  ename?: string;
  /** 结束时间 */
  endDate?: string;
  /** 已发放数量 */
  hasSendNum?: string;
  /**
   * 活动id
   * @format string
   */
  id?: string;
  /**
   * 奖品id
   * @format int32
   */
  prizeId?: number;
  /** 总数 */
  prizeQuantity?: string;
  /** 概率 */
  probability?: string;
  /** 可发放数 */
  remainPrizeQuantity?: string;
  /**
   * 权益类型id:7-实物(手工发货)
   * @format int32
   */
  rightTypeId?: number;
  /** 权益类型名称：实物(手工发货) */
  rightTypeName?: string;
  /**
   * 店铺ID
   * @format string
   */
  shopId?: string;
  /** 商品SkuCode */
  skuCode?: string;
  skuMainPicture?: string;
  /** 开始时间 */
  startDate?: string;
}

/** BizActivityBaseInfoVO */
export interface BizActivityBaseInfoVO {
  activityId?: string;
  activityName?: string;
  /** @format int32 */
  activityType?: number;
  atmosphereJson?: string;
  /** @format date-time */
  endTime?: string;
  /** @format string */
  id?: string;
  /** @format int32 */
  joinType?: number;
  rule?: string;
  shareContent?: string;
  shareImage?: string;
  shareTitle?: string;
  /** @format date-time */
  startTime?: string;
}

/** BizActivityCrowdVO */
export interface BizActivityCrowdVO {
  /** @format int32 */
  activityType?: number;
  configJson?: string;
  /** @format int32 */
  configType?: number;
  crowdName?: string;
  /** @format string */
  id?: string;
  interfaceClass?: string;
  /** @format int32 */
  orderBy?: number;
  remark?: string;
}

/** BizActivityDataLotteryRecordRequest */
export interface BizActivityDataLotteryRecordRequest {
  /**
   * 活动ID
   * @example "9a7bdcb09991717658d897344b5582ff"
   */
  activityId?: string;
  /**
   * 奖品类型
   * @format int32
   */
  lotteryType?: number;
  /**
   * 用户唯一ID
   * @example 456
   */
  ouid?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 领取状态：1-发送成功，2-发送失败
   * @format int32
   */
  receiveResult?: number;
  /** 结束时间 */
  receiveTimeEnd?: string;
  /** 开始时间 */
  receiveTimeStart?: string;
  /**
   * 用户昵称
   * @example 123
   */
  userNick?: string;
}

/** BizActivityDataLotteryRecordResponse */
export interface BizActivityDataLotteryRecordResponse {
  /** 关联活动 */
  activityName?: string;
  /**
   * 领取时间
   * @format date-time
   */
  createTime?: string;
  /** 手工单号 */
  generateId?: string;
  /** 奖品名称 */
  lotteryName?: string;
  /** 奖品类型 */
  lotteryType?: string;
  /**
   * 奖品类型数字
   * @format int32
   */
  lotteryTypeInt?: number;
  /** 备注 */
  mark?: string;
  /** openid */
  ouid?: string;
  /** 领取状态 */
  receiveStatus?: string;
  /** 发放订单号 */
  tid?: string;
  /** 用户昵称 */
  userNick?: string;
}

/** BizActivityDataOrderItemRequestVO */
export interface BizActivityDataOrderItemRequestVO {
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 订单ID
   * @example 4294732323378230000
   */
  tid?: string;
}

/** BizActivityDataOrderItemResponseVO */
export interface BizActivityDataOrderItemResponseVO {
  /** 购买件数 */
  num?: string;
  /** 商品实付总价 */
  payment?: string;
  /** 商品ID */
  skuId?: string;
  /** 商品名称 */
  title?: string;
}

/** BizActivityDataOrderRecordRequest */
export interface BizActivityDataOrderRecordRequest {
  /**
   * 活动ID
   * @example "9a7bdcb09991717658d897344b5582ff"
   */
  activityId?: string;
  /** 结束时间 */
  createTimeEnd?: string;
  /** 开始时间 */
  createTimeStart?: string;
  /** 订单状态 */
  orderStatus?: string;
  /**
   * 用户唯一ID
   * @example 456
   */
  ouid?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  tid?: string;
  /**
   * 用户昵称
   * @example 123
   */
  userNick?: string;
}

/** BizActivityDataOrderRecordRequestVO */
export interface BizActivityDataOrderRecordRequestVO {
  /**
   * 活动ID
   * @example "9a7bdcb09991717658d897344b5582ff"
   */
  activityId?: string;
  /** 结束时间 */
  createTimeEnd?: string;
  /** 开始时间 */
  createTimeStart?: string;
  /** 订单状态 */
  orderStatus?: string;
  /**
   * 用户唯一ID
   * @example 456
   */
  ouid?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  tid?: string;
  /**
   * 用户昵称
   * @example 123
   */
  userNick?: string;
}

/** BizActivityDataOrderRecordResponse */
export interface BizActivityDataOrderRecordResponse {
  /**
   * 下单时间
   * @format date-time
   */
  createTime?: string;
  /** 子订单编号 */
  oid?: string;
  /** 订单状态字段 */
  orderStatus?: string;
  /** 订单状态 */
  orderStatusStr?: string;
  /** openid */
  ouid?: string;
  /** 实付金额（元） */
  payment?: string;
  /** 应付金额（元） */
  price?: string;
  /** 订单编号 */
  tid?: string;
  /** 用户昵称 */
  userNick?: string;
}

/** BizActivityDataPhysicalPrizeInfoRequest */
export interface BizActivityDataPhysicalPrizeInfoRequest {
  /**
   * 活动ID
   * @example "9a7bdcb09991717658d897344b5582ff"
   */
  activityId?: string;
  /** 结束时间 */
  createTimeEnd?: string;
  /** 开始时间 */
  createTimeStart?: string;
  /**
   * 用户唯一ID
   * @example 456
   */
  ouid?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 用户昵称
   * @example 123
   */
  userNick?: string;
}

/** BizActivityDataPhysicalPrizeInfoResponse */
export interface BizActivityDataPhysicalPrizeInfoResponse {
  address?: string;
  area?: string;
  city?: string;
  /** 获奖时间 */
  createTime?: string;
  /** 奖品名称 */
  lotteryName?: string;
  /** 订单编号 */
  orderId?: string;
  /** openid */
  ouid?: string;
  phone?: string;
  province?: string;
  receiver?: string;
  /** 用户昵称 */
  userNick?: string;
}

/** BizActivityPlayVO */
export interface BizActivityPlayVO {
  activityId?: string;
  /** @format string */
  id?: string;
  orderAfterSign?: boolean;
  /** @format int32 */
  orderStatus?: number;
  /** @format date-time */
  orderTimeEnd?: string;
  /** @format date-time */
  orderTimeStart?: string;
  /** @format int32 */
  orderTimeType?: number;
  orderType?: string;
  productList?: BizActivityProductVO[];
  /** @format int32 */
  skuType?: number;
}

/** BizActivityProductVO */
export interface BizActivityProductVO {
  /** @format string */
  id?: string;
  numIid?: string;
  picUrl?: string;
  price?: string;
  /**
   * 销量
   * @format string
   */
  sellNum?: string;
  /** @format string */
  skuId?: string;
  title?: string;
}

/** BizActivityStairsVO */
export interface BizActivityStairsVO {
  /** @format string */
  id?: string;
  lottery?: BizLotteryVO[];
  name?: string;
  /** @format int32 */
  nums?: number;
  /** @format int32 */
  orderBy?: number;
  /** @format int32 */
  orderCountType?: number;
  payment?: string;
  singleOrderPayment?: string;
  /** @format int32 */
  tradeNum?: number;
}

/** BizCreateActivityPlanRequest */
export interface BizCreateActivityPlanRequest {
  endTime?: string;
  /** @format string */
  id?: string;
  liveRoomId?: string;
  /** @format int32 */
  orderBy?: number;
  planCouponDesc?: string;
  planCouponName?: string;
  planName?: string;
  /** @format int32 */
  planType?: number;
  startTime?: string;
}

/** BizCustomizeActivityInfoResponse */
export interface BizCustomizeActivityInfoResponse {
  actUrl?: string;
  activityProduct?: BizActivityProductVO[];
  activityStairs?: BizActivityStairsVO[];
  baseInfo?: BizActivityBaseInfoVO;
  cardJson?: string;
  /** @format int32 */
  cardSize?: number;
  cdpCrowdId?: string;
  crowdInfo?: BizActivityCrowdVO[];
  crowdName?: string;
  /** @format int32 */
  crowdType?: number;
  giftRule?: BizGiftRuleVO;
  playInfo?: BizActivityPlayVO;
  qrCodeUrl?: string;
  templateId?: string;
}

/** BizDelStairsLotteryRequest */
export interface BizDelStairsLotteryRequest {
  /** @format string */
  lotteryId?: string;
  /** @format int32 */
  num?: number;
  /** @format string */
  stairsId?: string;
}

/** BizExportRequest */
export interface BizExportRequest {
  exportParam?: string;
  exportType?: string;
}

/** BizGetActivityInfoRequest */
export interface BizGetActivityInfoRequest {
  activityId?: string;
  /** @format int32 */
  activityType?: number;
}

/** BizGetActivityListRequest */
export interface BizGetActivityListRequest {
  activityName?: string;
  /** @format int32 */
  activityStatus?: number;
  /** @format int32 */
  activityType?: number;
  /** @format int32 */
  cardSize?: number;
  createDateEnd?: string;
  createDateStart?: string;
  /** @format string */
  pageNo?: string;
  /** @format string */
  pageSize?: string;
}

/** BizGetActivityPlanRequest */
export interface BizGetActivityPlanRequest {
  activityName?: string;
  bannerUrl?: string;
  /** @format int32 */
  cardSize?: number;
  liveRoomId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** @format int32 */
  planType?: number;
  /** @format int32 */
  status?: number;
}

/** BizGetLotteryRecordRequest */
export interface BizGetLotteryRecordRequest {
  activityId?: string;
  activityName?: string;
  /** @format int32 */
  lotteryType?: number;
  ouid?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** @format int32 */
  receiveResult?: number;
  receiveTimeEnd?: string;
  receiveTimeStart?: string;
  userNick?: string;
}

/** BizGiftRuleVO */
export interface BizGiftRuleVO {
  activityId?: string;
  autoSend?: boolean;
  /** @format string */
  id?: string;
  /** @format int32 */
  reSubtractionType?: number;
  /** @format int32 */
  subtractionType?: number;
}

/** BizLotteryVO */
export interface BizLotteryVO {
  deliveryEndTime?: string;
  deliveryStartTime?: string;
  /** @format string */
  id?: string;
  lotteryName?: string;
  /** @format int32 */
  lotteryType?: number;
  lotteryValue?: string;
  price?: string;
  /** @format int32 */
  prizeNum?: number;
  remark?: string;
  showImage?: string;
}

/** BizSaveOrUpdateRequest */
export interface BizSaveOrUpdateRequest {
  activityProduct?: BizActivityProductVO[];
  activityStairs?: BizActivityStairsVO[];
  baseInfo?: BizActivityBaseInfoVO;
  cardJson?: string;
  /** @format int32 */
  cardSize?: number;
  cdpCrowdId?: string;
  crowdInfo?: BizActivityCrowdVO[];
  crowdName?: string;
  /** @format int32 */
  crowdType?: number;
  giftRule?: BizGiftRuleVO;
  playInfo?: BizActivityPlayVO;
  templateId?: string;
}

/** BizUpdateActivityPlanRequest */
export interface BizUpdateActivityPlanRequest {
  /** @format string */
  id?: string;
  /** @format int32 */
  orderBy?: number;
}

/** CollectingTankDTO */
export interface CollectingTankDTO {
  /** 品线名称 */
  lineName?: string[];
  /** 门槛数据 */
  thresholdData?: ActivityThresholdResponse[];
}

/** CollectingTankNewResponse */
export interface CollectingTankNewResponse {
  /** 活动时间 */
  activityTime?: string;
  /** 开卡人数 */
  openCardUser?: string;
  /** 付款金额 */
  payAmount?: string;
  /** 付款人数 */
  payUser?: string;
  /** PV */
  pv?: string;
  /** 领取人数 */
  receiveUser?: string;
  /** 阶梯数据 */
  stepDataResponseList?: StepDataResponse[];
  /** UV */
  uv?: string;
}

/** CollectingTankRequest */
export interface CollectingTankRequest {
  /**
   * 活动id
   * @format string
   */
  activityId?: string;
  /** 结束时间 */
  endDate?: string;
  /** 开始时间 */
  startDate?: string;
}

/** Conditions */
export interface Conditions {
  /** 最后一次下单时间条件 */
  lastOrderTime?: LastOrderTime;
  /** 会员等级条件 */
  memberLevel?: MemberLevelCondition;
  /** 新客户条件 */
  newCustomer?: EnabledFlag;
  /** 老客户条件 */
  oldCustomer?: EnabledFlag;
  /** 订单周期条件 */
  orderCycle?: OrderCycleCondition;
  /** 订单状态条件 */
  orderStatus?: OrderStatusCondition;
  /** 购买指定商品条件 */
  purchaseSpecificProduct?: PurchaseSpecificProductCondition;
  /** 累计订单金额条件 */
  totalOrderAmount?: TotalOrderAmountCondition;
  /** 累计订单数条件 */
  totalOrderCount?: TotalOrderCountCondition;
}

/** CurrentShop */
export interface CurrentShop {
  /** 店铺logo */
  logoUrl?: string;
  /**
   * 当前版本
   * @format int32
   */
  orderVersion?: number;
  /** 当前版本名称 */
  orderVersionName?: string;
  /**
   * 店铺id
   * @format string
   */
  shopId?: string;
  /** 店铺名称 */
  shopName?: string;
}

/** CurrentUser */
export interface CurrentUser {
  /** 登录账号 */
  account?: string;
  /**
   * 账户类型(0：未设置（默认），1:代运营账户，2：商家账号，3：开发者账号)
   * @format int32
   */
  accountType?: number;
  /** 用户权限类型：0：普通用户，1：管理员，2：超级管理员 */
  authorityType?: 'ADMIN' | 'NORMAL' | 'SUPER_ADMIN';
  /** 公司 */
  company?: string;
  /** 当前授权账号 */
  currentPin?: string;
  /** 首次登录用户需要修改密码 */
  firstLogin?: boolean;
  /** 访问者ip */
  ip?: string;
  /**
   * 最后登录时间
   * @format date-time
   */
  lastLoginTime?: string;
  /** 是否是主账号 */
  main?: boolean;
  /** 手机号 */
  mobile?: string;
  /** 用户名 */
  nickname?: string;
  /**
   * 所属组织Id
   * @format string
   */
  organizationId?: string;
  /** 密码是否过期 */
  passwordExpire?: boolean;
  /**
   * 是否签署协议
   * @format int32
   */
  sign?: number;
  /** token名称 */
  tokenName?: string;
  /** token值 */
  tokenValue?: string;
  /**
   * 用户id
   * @format string
   */
  userId?: string;
}

/** DyProductListRequest */
export interface DyProductListRequest {
  /** 商品标题 */
  name?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** 商品ID列表,逗号分割 */
  productIds?: string;
  /**
   * 商品状态：-1-全部； 0-在线；1-下线
   * @format string
   */
  status?: string;
}

/** DyProductListResponse */
export interface DyProductListResponse {
  cursorId?: string;
  data?: ProductInfoResponse[];
  /** @format string */
  page?: string;
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** DzCrowdConfig */
export interface DzCrowdConfig {
  activityType?: string;
  configJson?: string;
  /** @format int32 */
  configType?: number;
  crowdName?: string;
  /** @format string */
  id?: string;
  interfaceClass?: string;
  /** @format int32 */
  orderBy?: number;
  remark?: string;
}

/** DzShopGrade */
export interface DzShopGrade {
  deleted?: boolean;
  /** @format int32 */
  grade?: number;
  gradeName?: string;
  /** @format string */
  id?: string;
  userId?: string;
}

/** EnabledFlag */
export interface EnabledFlag {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
}

/** GetAchieveDetailRequest */
export interface GetAchieveDetailRequest {
  activityId?: string;
  ouid?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 状态：全部-1 未达成0 有达成1
   * @format int32
   */
  status?: number;
  userNick?: string;
}

/** GetAchieveInfoData */
export interface GetAchieveInfoData {
  stairsList?: Record<string, object>[];
  statisticsTime?: string;
}

/** GetDbDataVO */
export interface GetDbDataVO {
  /** @format int32 */
  clickNum?: number;
  opdate?: string;
  payment?: string;
  /** @format int32 */
  pv?: number;
  /** @format int32 */
  uv?: number;
}

/** GetEchartsDataRequestVO */
export interface GetEchartsDataRequestVO {
  activityId?: string;
  /** @format int32 */
  cardSize?: number;
  endDate?: string;
  orderType?: string[];
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  selectEndDate?: string;
  selectStartDate?: string;
  skuList?: string[];
  /** @format int32 */
  skuType?: number;
  startDate?: string;
  /** @format int32 */
  statusType?: number;
  type?: string;
}

/** GetEchartsDataResponse */
export interface GetEchartsDataResponse {
  activityEndTime?: string;
  activityName?: string;
  activityStartTime?: string;
  data?: object;
  statisticsTime?: string;
}

/** GetStyleListRequest */
export interface GetStyleListRequest {
  /** @format int32 */
  activityType?: number;
  /** @format string */
  pageNo?: string;
  /** @format string */
  pageSize?: string;
  templateId?: string;
}

/** GetTemplateListRequest */
export interface GetTemplateListRequest {
  /** @format int32 */
  activityType?: number;
  /** @format string */
  pageNo?: string;
  /** @format string */
  pageSize?: string;
}

/** LastOrderTime */
export interface LastOrderTime {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /** 会员等级列表 */
  value?: string[];
}

/** LiveRoomConfigResponse */
export interface LiveRoomConfigResponse {
  /**
   * 主键id
   * @format string
   */
  id?: string;
  /**
   * 活动跳转模式：1-自动进入高优先级活动；2-跳转通用活动列表
   * @format int32
   */
  jumpType?: number;
  /** 直播间名称 */
  liveName?: string;
  /** 直播间id */
  liveRoomId?: string;
  /** 小程序页面url */
  pageUrl?: string;
}

/** LiveRoomConfigUpdateRequest */
export interface LiveRoomConfigUpdateRequest {
  /**
   * 主键id
   * @format string
   */
  id?: string;
  /**
   * 活动跳转模式：1-自动进入高优先级活动；2-跳转通用活动列表
   * @format int32
   */
  jumpType?: number;
}

/** MemberLevelCondition */
export interface MemberLevelCondition {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /** 会员等级列表 */
  value?: string[];
}

/** MenuListRequest */
export interface MenuListRequest {
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
}

/** MenuListResponse */
export interface MenuListResponse {
  /** 是否禁止勾选节点复选框 */
  checkboxDisabled?: boolean;
  /** 子菜单 */
  children?: MenuListResponse[];
  /** icon */
  icon?: string;
  /**
   * 主键
   * @format string
   */
  id?: string;
  /** 菜单唯一标识 */
  keyName?: string;
  /**
   * 父级资源Id
   * @format string
   */
  parentId?: string;
  /**
   * 排序
   * @format int32
   */
  sort?: number;
  /** 资源标题 */
  title?: string;
  /** 路径 */
  url?: string;
}

/** MenuRequest */
export type MenuRequest = object;

/** MinMaxValue */
export interface MinMaxValue {
  /**
   * 最大值
   * @format int32
   */
  max?: number;
  /**
   * 最小值
   * @format int32
   */
  min?: number;
}

/** Number */
export type Number = object;

/** OrderCycleCondition */
export interface OrderCycleCondition {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /** 订单周期配置值 */
  value?: OrderCycleValue;
}

/** OrderCycleValue */
export interface OrderCycleValue {
  /** 指定时间内累计订单（type=2时使用，可以是日期范围字符串） */
  dateRange?: string[];
  /**
   * 在多少天内累计订单（type=1时使用）
   * @format int32
   */
  days?: number;
  /**
   * 类型：1-在多少天内累计订单，2-指定时间内累计订单
   * @format int32
   */
  type?: number;
}

/** OrderStatusCondition */
export interface OrderStatusCondition {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /**
   * 订单状态值：1-已付款未关闭，2-已收获
   * @format int32
   */
  value?: number;
}

/** OssUploadResponse */
export interface OssUploadResponse {
  etag?: string;
  fileName?: string;
  url?: string;
  versionId?: string;
}

/** PermissionListByRoleIdRequest */
export interface PermissionListByRoleIdRequest {
  /**
   * 角色ID
   * @format string
   */
  roleId?: string;
}

/** PermissionListResponse */
export interface PermissionListResponse {
  /**
   * 功能ID
   * @format string
   */
  functionId?: string;
  /** 功能key */
  key?: string;
  /** 功能名称 */
  name?: string;
}

/** PhoneLoginRequest */
export interface PhoneLoginRequest {
  /** 验证码 */
  code?: string;
  /** 手机号 */
  mobile?: string;
}

/** PrizeCouponRequest */
export interface PrizeCouponRequest {
  couponMetaId?: string;
}

/** PrizeCouponResponse */
export interface PrizeCouponResponse {
  /** 发券确认的回调url */
  callbackUrl?: string;
  /** 券的使用须知 */
  consumeDesc?: string;
  /** 当前券模版的去使用链接，用户领取后可在直播间、卡包等场景点击「去使用」后跳转到「该链接 */
  consumePath?: string;
  /** 抖音开放平台券模板ID */
  couponMetaId?: string;
  /** 小程序券名称 */
  couponName?: string;
  /**
   * 优惠金额，单位分
   * @format string
   */
  discountAmount?: string;
  /** 券类型  DirectReduce=1 立减券 FullReduce=2 满减券 Consume=3 权益券 AdFree=5 免广告券 */
  discountType?: Number;
  /**
   * 当discount_type=3且related_type=4时返回该参数
   * @format string
   */
  freeEpNumber?: string;
  /** 商家券模板号 */
  merchantMetaNo?: string;
  /**
   * 满减门槛金额，单位分
   * @format string
   */
  minPayAmount?: string;
  /** 对于本地商品和泛知识课程的映射ID */
  originId?: string;
  /**
   * 券模板生效时间（即可领取开始时间
   * @format string
   */
  receiveBeginTime?: string;
  /** 券的领取须知 */
  receiveDesc?: string;
  /**
   * 券模板过期时间（即可领取结束时间）
   * @format string
   */
  receiveEndTime?: string;
  /** 券关联类型 Course=1 泛知识课程 Life=2 本地商品 General=3 没有关联关系的普通权益 Playlet=4 免费看剧券 */
  relatedType?: Number;
  /** 小程序开发配置中的密钥 */
  secretSource?: Number;
  /** 券状态  ToBeValid=1 待生效 Valid=2 生效中 Invalid=3 失效 Expired=4 已过期 Deleted=5 已删除 Pause=6 下架停用（可恢复） Reviewing=7 审核中 ReviewDenied=8 审核不通过 */
  status?: Number;
  /**
   * 券模板最大库存，最大值 1000000000；此库存是该券可发放的总库存，所有发券人共享
   * @format string
   */
  stockNumber?: string;
  /**
   * 用户领取的「券记录」的有效期开始时间（单位秒）
   * @format string
   */
  validBeginTime?: string;
  /**
   * 用户领取的「券记录」的有效时长（单位秒）
   * @format string
   */
  validDuration?: string;
  /**
   * 用户领取的「券记录」的有效期结束时间（单位秒）
   * @format string
   */
  validEndTime?: string;
  /** 券有效期类型  ValidRegularDay=1 固定时间，所有用户领券有效期一致 ValidAfterReceive=2 领取后生效 */
  validType?: Number;
}

/** PrizeSkuAddRequestVO */
export interface PrizeSkuAddRequestVO {
  /**
   * 总库存
   * @format string
   */
  quantityTotal?: string;
  /** 商品编码 */
  skuCode?: string;
  /** 商品主图 */
  skuMainPicture?: string;
  /** 奖品名称 */
  skuName?: string;
  /** 奖品价值 */
  skuValue?: string;
}

/** PrizeSkuAddStockRequestVO */
export interface PrizeSkuAddStockRequestVO {
  /**
   * 增加数量
   * @format string
   */
  addQuantity?: string;
  /** 商品code */
  skuCode?: string;
}

/** PrizeSkuDealRequestVO */
export interface PrizeSkuDealRequestVO {
  /** 订单编号 */
  sendCode?: string;
}

/** PrizeSkuDeleteRequestVO */
export interface PrizeSkuDeleteRequestVO {
  /** 商品code */
  skuCode?: string;
}

/** PrizeSkuOrderExportResponseVO */
export interface PrizeSkuOrderExportResponseVO {
  /** 失败列表 */
  failList?: string[];
  /**
   * 失败数
   * @format int32
   */
  failNum?: number;
  /**
   * 成功数
   * @format int32
   */
  successNum?: number;
  /**
   * 导入总数
   * @format int32
   */
  totalNum?: number;
}

/** PrizeSkuOrderPageResponseVO */
export interface PrizeSkuOrderPageResponseVO {
  /** 关联活动名称 */
  activityName?: string;
  /** 详细地址信息 */
  address?: string;
  /** 获奖订单号（子单号） */
  awardSendCode?: string;
  /** 取消原因 */
  cancelReason?: string;
  /** 城市名称 */
  city?: string;
  /** 区县 */
  county?: string;
  /**
   * 创建开始时间
   * @format date-time
   */
  createTime?: string;
  /** 发货状态（0-待发货，1-已发货,2-已取消） */
  dealStatusName?: string;
  /** 收件人手机号 */
  mobile?: string;
  /** 昵称 */
  nickName?: string;
  /** openId */
  openId?: string;
  /** 奖品(商品)名称 */
  prizeName?: string;
  /** 省份名称 */
  province?: string;
  /** 真实姓名（收件人） */
  realName?: string;
  /** 订单编号 */
  sendCode?: string;
}

/** PrizeSkuOrderRequestVO */
export interface PrizeSkuOrderRequestVO {
  /** 关联活动名称 */
  activityName?: string;
  /**
   * 活动类型：1
   * @format int32
   */
  activityType?: number;
  /**
   * 创建结束时间
   * @format date-time
   */
  createEndTime?: string;
  /**
   * 创建开始时间
   * @format date-time
   */
  createStartTime?: string;
  /**
   * 发货状态（全部：-1；0-待发货；1-已发货）
   * @format int32
   */
  dealStatus?: number;
  /** 昵称 */
  nickName?: string;
  /** openId */
  openId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** 奖品(商品)名称 */
  prizeName?: string;
  /** 订单编号 */
  sendCode?: string;
  /**
   * 店铺id
   * @format string
   */
  shopId?: string;
}

/** PrizeSkuPageRequestVO */
export interface PrizeSkuPageRequestVO {
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /** 奖品名称 */
  skuName?: string;
}

/** PrizeSkuRelationActivityPageRequestVO */
export interface PrizeSkuRelationActivityPageRequestVO {
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  skuCode?: string;
}

/** PrizeSkuRelationActivityPageResponseVO */
export interface PrizeSkuRelationActivityPageResponseVO {
  /**
   * 活动占用资产数量
   * @format string
   */
  activityFreezeNum?: string;
  /** 活动ID */
  activityId?: string;
  /** 活动名称 */
  activityName?: string;
  /** 活动类型 */
  activityType?: string;
  /**
   * 活动结束时间
   * @format date-time
   */
  endDate?: string;
  /**
   * 活动开始时间
   * @format date-time
   */
  startDate?: string;
}

/** ProductInfoResponse */
export interface ProductInfoResponse {
  name?: string;
  /**
   * 商品ID
   * @format string
   */
  numIid?: string;
  /** 商品主图 */
  picUrl?: string;
  /**
   * 销量
   * @format string
   */
  sellNum?: string;
  /** 商品名称 */
  title?: string;
}

/** PromotionVO */
export interface PromotionVO {
  message?: string;
  promotionName?: string;
  showStatus?: boolean;
  /** @format int32 */
  type?: number;
}

/** PurchaseSpecificProductCondition */
export interface PurchaseSpecificProductCondition {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /** 商品列表 */
  value?: ProductInfoResponse[];
}

/** RoleAddRequest */
export interface RoleAddRequest {
  /** 角色描述 */
  remark?: string;
  /** 角色名称 */
  title?: string;
}

/** RoleBindMenuRequest */
export interface RoleBindMenuRequest {
  /** 功能ID */
  functionList?: string[];
  /** 菜单ID */
  menuList?: string[];
  /**
   * 角色ID
   * @format string
   */
  roleId?: string;
}

/** RoleDelRequest */
export interface RoleDelRequest {
  /**
   * 角色id
   * @format string
   */
  id?: string;
}

/** RoleListRequest */
export interface RoleListRequest {
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 角色状态 -1 全部 0-禁用 1-启用
   * @format int32
   */
  status?: number;
  /** 角色名称 */
  title?: string;
}

/** RoleListResponse */
export interface RoleListResponse {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /**
   * 角色id
   * @format string
   */
  id?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 角色父级Id
   * @format string
   */
  parentId?: string;
  /** 角色描述 */
  remark?: string;
  /**
   * 店铺Id
   * @format string
   */
  shopId?: string;
  /**
   * 角色状态 空-全部 1-启用 2-禁用
   * @format int32
   */
  status?: number;
  /** 角色名称 */
  title?: string;
  /**
   * 更新时间
   * @format date-time
   */
  updateTime?: string;
}

/** RoleUpdateRequest */
export interface RoleUpdateRequest {
  /**
   * 角色id
   * @format string
   */
  id?: string;
  /** 菜单ID */
  menuList?: string[];
  /** 角色描述 */
  remark?: string;
  /**
   * 角色状态 1-启用 2-禁用
   * @format int32
   */
  status?: number;
  /** 角色名称 */
  title?: string;
}

/** SelectShopResponse */
export interface SelectShopResponse {
  /**
   * 店铺ID
   * @format string
   */
  shopId?: string;
}

/** ShopInfoResponse */
export interface ShopInfoResponse {
  /**
   * 店铺id
   * @format string
   */
  id?: string;
  /** logo */
  logo?: string;
  /** 店铺logo */
  logoUrl?: string;
  /**
   * 服务市场授权结束时间（不可以手动调整）
   * @format date-time
   */
  marketEndTime?: string;
  /**
   * 订购过期时间
   * @format date-time
   */
  orderExpireTime?: string;
  /**
   * 订购剩余天数
   * @format string
   */
  orderRemainingDays?: string;
  /**
   * 当前版本
   * @format int32
   */
  orderVersion?: number;
  /** 当前版本 */
  orderVersionName?: string;
  /**
   * 组织架构id
   * @format string
   */
  organizationId?: string;
  /** 组织名称 */
  organizationName?: string;
  /**
   * 店铺id
   * @format string
   */
  shopId?: string;
  /** 店铺名称 */
  shopName?: string;
  /**
   * 商家类型0：SOP1：FBP2：LBP5：SOPL
   * @format int32
   */
  shopType?: number;
  /**
   * 排序
   * @format int32
   */
  sort?: number;
  /**
   * 状态（0-初始化中,1-正常,2-冻结,3-过期）
   * @format int32
   */
  status?: number;
  /**
   * 品牌Id
   * @format string
   */
  venderId?: string;
  /**
   * 商家入驻类型：0pop商家,1自营店铺
   * @format int32
   */
  venderType?: number;
}

/** ShopListResponse */
export interface ShopListResponse {
  /** logo */
  logo?: string;
  /**
   * 订购过期时间
   * @format date-time
   */
  orderExpireTime?: string;
  /**
   * 订购开始时间
   * @format date-time
   */
  orderStartTime?: string;
  /** 是否是试用版本 */
  orderTrialVersion?: boolean;
  /**
   * 当前版本
   * @format int32
   */
  orderVersion?: number;
  /** 当前版本名称 */
  orderVersionName?: string;
  /**
   * 店铺Id
   * @format string
   */
  shopId?: string;
  /** 店铺名称 */
  shopName?: string;
  /**
   * 开店时间
   * @format date-time
   */
  shopOpenTime?: string;
  /**
   * 排序
   * @format int32
   */
  sort?: number;
}

/** StepDataResponse */
export interface StepDataResponse {
  /** 付款金额 */
  payAmount?: string;
  /** 付款人数 */
  payUser?: string;
  /** 领取人数 */
  receiveUser?: string;
  /**
   * 系列ID
   * @format string
   */
  seriesId?: string;
  /** 系列名称 */
  seriesName?: string;
  /**
   * 系列顺序
   * @format int32
   */
  seriesSort?: number;
  /**
   * 阶梯ID
   * @format string
   */
  stepId?: string;
  /** 阶梯名称 */
  stepName?: string;
  /**
   * 阶梯顺序
   * @format int32
   */
  stepSort?: number;
}

/** ThresholdInfo */
export interface ThresholdInfo {
  /** 筛选条件配置 */
  conditions?: Conditions;
  /** cdpId */
  crowdId?: string;
  /** 客户群体名称 */
  crowdName?: string;
}

/** TotalOrderAmountCondition */
export interface TotalOrderAmountCondition {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /** 金额范围配置 */
  value?: MinMaxValue;
}

/** TotalOrderCountCondition */
export interface TotalOrderCountCondition {
  /** 是否启用该条件 */
  enabled?: boolean;
  /** @format int32 */
  thresholdId?: number;
  /** 订单数量范围配置 */
  value?: MinMaxValue;
}

/** UpdateActivityOrderByRequest */
export interface UpdateActivityOrderByRequest {
  /** 活动id */
  activityId?: string;
  /**
   * 优先级
   * @format int32
   */
  orderBy?: number;
}

/** UserAddRequest */
export interface UserAddRequest {
  /** 账号 */
  account?: string;
  /** 手机号码 */
  mobile?: string;
  /** 昵称 */
  nickname?: string;
  /** 登录密码 */
  password?: string;
  /**
   * 角色ID
   * @format string
   */
  roleId?: string;
}

/** UserDelRequest */
export interface UserDelRequest {
  /** @format string */
  id?: string;
}

/** UserListRequest */
export interface UserListRequest {
  /** 账号名称 */
  account?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 角色
   * @format string
   */
  roleId?: string;
  /**
   * 状态 1- 全部 1-正常 2-禁用
   * @format int32
   */
  status?: number;
}

/** UserListResponse */
export interface UserListResponse {
  /** 账号 */
  account?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /** @format string */
  id?: string;
  /** 手机号码 */
  mobile?: string;
  /** 昵称 */
  nickname?: string;
  /** 登录密码 */
  password?: string;
  /**
   * 角色ID
   * @format string
   */
  roleId?: string;
  /** 角色名称 */
  roleName?: string;
  /**
   * 账号状态
   * @format int32
   */
  status?: number;
  /**
   * 更新时间
   * @format date-time
   */
  updateTime?: string;
}

/** UserLoginRequest */
export interface UserLoginRequest {
  /** 账号 */
  account: string;
  /** 密码 */
  password: string;
}

/** UserUpdateRequest */
export interface UserUpdateRequest {
  /** 账号 */
  account?: string;
  /**
   * 用户ID
   * @format string
   */
  id?: string;
  /** 手机号码 */
  mobile?: string;
  /** 昵称 */
  nickname?: string;
  /** 登录密码 */
  password?: string;
  /**
   * 角色ID
   * @format string
   */
  roleId?: string;
  /**
   * 状态 1-正常 2-禁用
   * @format int32
   */
  status?: number;
}

/** WarningInfoRequestVO */
export interface WarningInfoRequestVO {
  /** 活动名称 */
  activityName?: string;
  /** openid */
  openId?: string;
  /**
   * 当前请求的页码，默认：1
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 1
   */
  pageNum: number;
  /**
   * 每页显示的数量，默认：20，最大不超过500
   * @format int32
   * @min 1
   * @exclusiveMin false
   * @max 100
   * @exclusiveMax false
   * @example 20
   */
  pageSize: number;
  /**
   * 领奖结束时间
   * @format date-time
   */
  receiveEndTime?: string;
  /**
   * 领奖开始时间
   * @format date-time
   */
  receiveStartTime?: string;
  /**
   * 订单编号
   * @format string
   */
  tid?: string;
}

/** WarningInfoResponseVO */
export interface WarningInfoResponseVO {
  activityName?: string;
  lotteryName?: string;
  lotteryTypeStr?: string;
  nickname?: string;
  openId?: string;
  /** @format date-time */
  orderCreateTime?: string;
  orderStatusStr?: string;
  /** @format date-time */
  participateTime?: string;
  receiveStatusStr?: string;
  /** @format date-time */
  receiveTime?: string;
  remark?: string;
  tid?: string;
  warningStatement?: string;
}

/**
 * DzLotteryConfig对象
 * 定制抽奖配置表
 */
export interface DzLotteryConfig {
  /** 活动id */
  activityId?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string;
  /**
   * 每日限制奖品份数
   * @format int32
   */
  dayPrizeLimit?: number;
  /** 活动状态（0-正常，1-删除） */
  deleted?: boolean;
  deliveryEndTime?: string;
  /** 预计发货时间 */
  deliveryStartTime?: string;
  /** 品线 */
  goodsLine?: string;
  /**
   * 自增id
   * @format string
   */
  id?: string;
  /** 奖品名称 */
  lotteryName?: string;
  /**
   * 对应资产的剩余库存
   * @format int32
   */
  lotteryRemainNum?: number;
  /**
   * 奖品类型
   * @format int32
   */
  lotteryType?: number;
  /** 简单值（可直接发放的),权益计划id等 */
  lotteryValue?: string;
  /**
   * 奖品位置
   * @format int32
   */
  position?: number;
  price?: string;
  /**
   * 奖品id
   * @format string
   * @example 1
   */
  prizeId?: string;
  /**
   * 可抽取的奖品总数
   * @format int32
   */
  prizeNum?: number;
  /**
   * 中奖几率
   * @format int32
   */
  probability?: number;
  remark?: string;
  /**
   * 段数
   * @format int32
   */
  sectionNum?: number;
  /**
   * 已抽取的奖品数量
   * @format int32
   */
  sendPrizeNum?: number;
  /** 展现的图片地址 */
  showImage?: string;
  /**
   * 顺序
   * @format int32
   * @example 1
   */
  sortId?: number;
  /**
   * 状态 0-下架 1-上架
   * @format int32
   */
  status?: number;
  /**
   * 单次发放数量
   * @format int32
   */
  unitCount?: number;
  /**
   * 修改时间
   * @format date-time
   */
  updateTime?: string;
  /** 商户ID */
  userId?: string;
}

/** IPage«Activity10002DrawChanceRecordResponse» */
export interface IPageActivity10002DrawChanceRecordResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10002DrawChanceRecordResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«Activity10002PhysicalPrizeInfoResponse» */
export interface IPageActivity10002PhysicalPrizeInfoResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10002PhysicalPrizeInfoResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«Activity10002PrizeRecordResponse» */
export interface IPageActivity10002PrizeRecordResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10002PrizeRecordResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«Activity10201UserPotRecordPageResponse» */
export interface IPageActivity10201UserPotRecordPageResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10201UserPotRecordPageResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«Activity10201UserWinningLogResponse» */
export interface IPageActivity10201UserWinningLogResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10201UserWinningLogResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«Activity10202UserAfterWinningLogResponse» */
export interface IPageActivity10202UserAfterWinningLogResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10202UserAfterWinningLogResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«Activity10202UserWinningLogResponse» */
export interface IPageActivity10202UserWinningLogResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: Activity10202UserWinningLogResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«AlibabaBenefitCenterVO» */
export interface IPageAlibabaBenefitCenterVO {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: AlibabaBenefitCenterVO[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«BizActivityDataLotteryRecordResponse» */
export interface IPageBizActivityDataLotteryRecordResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: BizActivityDataLotteryRecordResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«BizActivityDataOrderItemResponseVO» */
export interface IPageBizActivityDataOrderItemResponseVO {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: BizActivityDataOrderItemResponseVO[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«BizActivityDataOrderRecordResponse» */
export interface IPageBizActivityDataOrderRecordResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: BizActivityDataOrderRecordResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«BizActivityDataPhysicalPrizeInfoResponse» */
export interface IPageBizActivityDataPhysicalPrizeInfoResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: BizActivityDataPhysicalPrizeInfoResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«PrizeSkuOrderPageResponseVO» */
export interface IPagePrizeSkuOrderPageResponseVO {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: PrizeSkuOrderPageResponseVO[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«PrizeSkuRelationActivityPageResponseVO» */
export interface IPagePrizeSkuRelationActivityPageResponseVO {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: PrizeSkuRelationActivityPageResponseVO[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«RoleListResponse» */
export interface IPageRoleListResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: RoleListResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«UserListResponse» */
export interface IPageUserListResponse {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: UserListResponse[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** IPage«WarningInfoResponseVO» */
export interface IPageWarningInfoResponseVO {
  /** @format string */
  current?: string;
  /** @format string */
  pages?: string;
  records?: WarningInfoResponseVO[];
  /** @format string */
  size?: string;
  /** @format string */
  total?: string;
}

/** R«ActivityData» */
export interface RActivityData {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: ActivityData;
  /** 错误消息 */
  message?: string;
}

/** R«BizCustomizeActivityInfoResponse» */
export interface RBizCustomizeActivityInfoResponse {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: BizCustomizeActivityInfoResponse;
  /** 错误消息 */
  message?: string;
}

/** R«GetAchieveInfoData» */
export interface RGetAchieveInfoData {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: GetAchieveInfoData;
  /** 错误消息 */
  message?: string;
}

/** R«GetEchartsDataResponse» */
export interface RGetEchartsDataResponse {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: GetEchartsDataResponse;
  /** 错误消息 */
  message?: string;
}

/** R«IPage«BizActivityDataLotteryRecordResponse»» */
export interface RIPageBizActivityDataLotteryRecordResponse {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: IPageBizActivityDataLotteryRecordResponse;
  /** 错误消息 */
  message?: string;
}

/** R«IPage«BizActivityDataOrderItemResponseVO»» */
export interface RIPageBizActivityDataOrderItemResponseVO {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: IPageBizActivityDataOrderItemResponseVO;
  /** 错误消息 */
  message?: string;
}

/** R«IPage«BizActivityDataOrderRecordResponse»» */
export interface RIPageBizActivityDataOrderRecordResponse {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: IPageBizActivityDataOrderRecordResponse;
  /** 错误消息 */
  message?: string;
}

/** R«IPage«BizActivityDataPhysicalPrizeInfoResponse»» */
export interface RIPageBizActivityDataPhysicalPrizeInfoResponse {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: IPageBizActivityDataPhysicalPrizeInfoResponse;
  /** 错误消息 */
  message?: string;
}

/** R«IPage«WarningInfoResponseVO»» */
export interface RIPageWarningInfoResponseVO {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: IPageWarningInfoResponseVO;
  /** 错误消息 */
  message?: string;
}

/** R«List«DzCrowdConfig»» */
export interface RListDzCrowdConfig {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: DzCrowdConfig[];
  /** 错误消息 */
  message?: string;
}

/** R«List«DzShopGrade»» */
export interface RListDzShopGrade {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: DzShopGrade[];
  /** 错误消息 */
  message?: string;
}

/** R«List«GetDbDataVO»» */
export interface RListGetDbDataVO {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: GetDbDataVO[];
  /** 错误消息 */
  message?: string;
}

/** R«List«Map«string,object»»» */
export interface RListMapStringObject {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: Record<string, object>[];
  /** 错误消息 */
  message?: string;
}

/** R«List«PromotionVO»» */
export interface RListPromotionVO {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: PromotionVO[];
  /** 错误消息 */
  message?: string;
}

/** R«Map«string,object»» */
export interface RMapStringObject {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: object;
  /** 错误消息 */
  message?: string;
}

/** R«boolean» */
export interface RBoolean {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: boolean;
  /** 错误消息 */
  message?: string;
}

/** R«object» */
export interface RObject {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: object;
  /** 错误消息 */
  message?: string;
}

/** R«string» */
export interface RString {
  /**
   * 错误状态码，正确返回的时候值为200
   * @format int32
   */
  code?: number;
  /** 内容 */
  data?: string;
  /** 错误消息 */
  message?: string;
}
