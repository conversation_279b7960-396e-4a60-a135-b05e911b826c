import {
  Activity10202CreateOrUpdateRequest,
  Activity10202CreateOrUpdateResponse,
  Activity10202SectionRequest,
  Activity10202SectionResponse,
  Activity10202SkuAfterTemplate,
  Activity10202SkuBeforeTemplate,
  Activity10202SkuDemoResponse,
  Activity10202UserAfterWinningLogRequest,
  Activity10202UserTotalWinningLogRequest,
  Activity10202UserTotalWinningLogResponse,
  Activity10202UserWinningLogRequest,
  IPageActivity10202UserAfterWinningLogResponse,
  IPageActivity10202UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 伊利定制-转段有礼
 * @summary 创建活动
 * @request POST:/10202/createActivity
 */
export const createActivity = (
  request: Activity10202CreateOrUpdateRequest,
): Promise<Activity10202CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10202/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利转段礼记录
 * @summary 复购奖品领取记录
 * @request POST:/10202/data/afterWinningLog
 */
export const dataAfterWinningLog = (
  activity10202UserWinningLogRequest: Activity10202UserAfterWinningLogRequest,
): Promise<IPageActivity10202UserAfterWinningLogResponse> => {
  return httpRequest({
    url: '/10202/data/afterWinningLog',
    method: 'post',
    data: activity10202UserWinningLogRequest,
  });
};

/**
 * @tags 伊利转段礼记录
 * @summary 复购奖品领取记录导出
 * @request POST:/10202/data/afterWinningLog/export
 */
export const dataAfterWinningLogExport = (
  activity10202UserWinningLogRequest: Activity10202UserAfterWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/10202/data/afterWinningLog/export',
    method: 'post',
    data: activity10202UserWinningLogRequest,
  });
};

/**
 * @tags 伊利转段礼记录
 * @summary 小罐奖品领取记录
 * @request POST:/10202/data/demoWinningLog
 */
export const dataDemoWinningLog = (
  activity10202UserWinningLogRequest: Activity10202UserWinningLogRequest,
): Promise<IPageActivity10202UserWinningLogResponse> => {
  return httpRequest({
    url: '/10202/data/demoWinningLog',
    method: 'post',
    data: activity10202UserWinningLogRequest,
  });
};

/**
 * @tags 伊利转段礼记录
 * @summary 小罐奖品领取记录导出
 * @request POST:/10202/data/demoWinningLog/export
 */
export const dataDemoWinningLogExport = (
  activity10202UserWinningLogRequest: Activity10202UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/10202/data/demoWinningLog/export',
    method: 'post',
    data: activity10202UserWinningLogRequest,
  });
};

/**
 * @tags 伊利转段礼记录
 * @summary 获取活动的品线和段位
 * @request POST:/10202/data/getSectionInfo
 */
export const dataGetSectionInfo = (request: Activity10202SectionRequest): Promise<Activity10202SectionResponse> => {
  return httpRequest({
    url: '/10202/data/getSectionInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利转段礼记录
 * @summary 总获奖记录查询
 * @request POST:/10202/data/totalWinningLog
 */
export const dataTotalWinningLog = (
  activity10202UserWinningLogRequest: Activity10202UserTotalWinningLogRequest,
): Promise<Activity10202UserTotalWinningLogResponse[]> => {
  return httpRequest({
    url: '/10202/data/totalWinningLog',
    method: 'post',
    data: activity10202UserWinningLogRequest,
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 导入后置sku信息excel
 * @request POST:/10202/importSkuExcelAfter
 */
export const importSkuExcelAfter = (file: any): Promise<Activity10202SkuAfterTemplate[]> => {
  return httpRequest({
    url: '/10202/importSkuExcelAfter',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 导入前置sku信息excel
 * @request POST:/10202/importSkuExcelBefore
 */
export const importSkuExcelBefore = (file: any): Promise<Activity10202SkuBeforeTemplate[]> => {
  return httpRequest({
    url: '/10202/importSkuExcelBefore',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 导入小罐sku信息excel
 * @request POST:/10202/importSkuExcelDemo
 */
export const importSkuExcelDemo = (file: any): Promise<Activity10202SkuDemoResponse[]> => {
  return httpRequest({
    url: '/10202/importSkuExcelDemo',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 下载后置sku模板
 * @request POST:/10202/skuTemplateAfter/export
 */
export const skuTemplateAfterExport = (): Promise<void> => {
  return httpRequest({
    url: '/10202/skuTemplateAfter/export',
    method: 'post',
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 下载前置sku模板
 * @request POST:/10202/skuTemplateBefore/export
 */
export const skuTemplateBeforeExport = (): Promise<void> => {
  return httpRequest({
    url: '/10202/skuTemplateBefore/export',
    method: 'post',
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 下载小罐sku模板
 * @request POST:/10202/skuTemplateDemo/export
 */
export const skuTemplateDemoExport = (): Promise<void> => {
  return httpRequest({
    url: '/10202/skuTemplateDemo/export',
    method: 'post',
  });
};

/**
 * @tags 伊利定制-转段有礼
 * @summary 修改活动
 * @request POST:/10202/updateActivity
 */
export const updateActivity = (
  request: Activity10202CreateOrUpdateRequest,
): Promise<Activity10202CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10202/updateActivity',
    method: 'post',
    data: request,
  });
};
