import styles from './index.module.scss';
import Intro from '@/components/Intro';
import Index from '@/components/Container';
import ActivitySkinSelector from './components/ActivitySkinSelector/ActivitySkinSelector';


const instructions = `1、活动效果：提升会员复购转化
2、活动门槛支持设置会员，满足活动门槛的买家才可参与活动；奖品支持优惠券`;

export default function CustomActivity() {
  return (
    <div className={styles.container}>
      <Intro
        activityName="会员转段双享礼"
        docLinkText=""
        docLink=""
        instructions={instructions}
      />
      <Index
        title="选择模板"
        style={{ padding: 20, marginTop: 20 }}
      >
        <ActivitySkinSelector activityType={10202} />
      </Index>
    </div>
  );
}
