import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateRepurchase } from './validator';
import { ModuleType, RepurchaseState } from '@/pages/activity/10202/create/type';

export function useRepurchase() {
  const { state, dispatch } = useActivity();

  const updateRepurchase = (data: Partial<RepurchaseState>) => {
    dispatch({
      type: 'UPDATE_REPURCHASE',
      payload: data,
    });

    // 清除错误
    dispatch({
      type: 'CLEAR_ERRORS',
      module: ModuleType.REPURCHASE,
    });
  };

  const validateRepurchaseData = () => {
    const errors = validateRepurchase(state);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.REPURCHASE,
      payload: errors,
    });
    return errors.length === 0;
  };

  return {
    repurchasePrizeList: state.repurchase.repurchasePrizeList,
    repurchaseSkuList: state.repurchase.repurchaseSkuList,
    repurchase: state.repurchase,
    updateRepurchase,
    validateRepurchase: validateRepurchaseData,
    errors: state.errors[ModuleType.REPURCHASE],
  };
}