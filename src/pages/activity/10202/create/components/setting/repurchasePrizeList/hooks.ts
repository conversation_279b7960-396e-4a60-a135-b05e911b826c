import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateRepurchasePrizeList } from './validator';
import { ModuleType, RepurchasePrizeListState } from '@/pages/activity/10202/create/type';

export function useRepurchasePrizeList() {
  const { state, dispatch } = useActivity();

  const updateRepurchasePrizeList = (data: Partial<RepurchasePrizeListState>) => {
    dispatch({
      type: 'UPDATE_REPURCHASE_PRIZE_LIST',
      payload: data,
    });

    // 清除错误
    dispatch({
      type: 'CLEAR_ERRORS',
      module: ModuleType.REPURCHASE_PRIZE_LIST,
    });
  };

  const validateRepurchasePrizeListData = () => {
    const errors = validateRepurchasePrizeList(state);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.REPURCHASE_PRIZE_LIST,
      payload: errors,
    });
    return errors.length === 0;
  };

  return {
    repurchasePrizeList: state.repurchasePrizeList,
    updateRepurchasePrizeList,
    validateRepurchasePrizeList: validateRepurchasePrizeListData,
    errors: state.errors[ModuleType.REPURCHASE_PRIZE_LIST],
  };
}