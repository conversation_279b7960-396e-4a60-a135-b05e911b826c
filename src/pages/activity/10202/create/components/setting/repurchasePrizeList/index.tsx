import Container from '@/components/Container';
import { Box, Button, Dialog, Form, Input, Message, NumberPicker, Radio, Table } from '@alifd/next';
import { useActivity } from '../../../reducer';
import { ModuleType } from '@/pages/activity/10202/create/type';
import { useRepurchasePrizeList } from './hooks';
import { getPrizeTypeLabel, PrizeTypeEnum } from '@/utils';
import { ACTIVITY_STATUS } from '@/utils/constant';
import choosePrize from '../demoSkuList/components/choosePrize/choosePrize';
import editPrize from '../demoSkuList/components/editPrize';
import { activityCustomizeActivityMinusLotteryNum } from '@/api/b';

const FormItem = Form.Item;
const initPrizeItem = {
    lotteryName: '',
    lotteryType: '',
    lotteryValue: '',
    prizeNum: 1,
    dayLimitType: 2,
    awardLimitType: 2,
    awardLimitCount: 1,
    sectionNum: 1,
    goodsLine: '',
    showImage: '',
    price: '0',
    isAddNew: true,
};

const WarningContent = () => {
    return (
      <Box direction={'column'} spacing={10} align={'center'}>
        <div>编辑奖品将释放已冻结库存</div>
        <div><span className="text-red">请务必重新完整保存活动</span>，是否继续</div>
      </Box>
    );
};

export default function RepurchasePrizeList() {
  const { state, dispatch } = useActivity();
  const { repurchasePrizeList, updateRepurchasePrizeList } = useRepurchasePrizeList();

  const { operationType, activityStatus } = state.extra;
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const isView = operationType === 'view';
  const isEdit = operationType === 'edit';

  // 确保复购奖品列表至少有一个初始项
  const prizeList = Array.isArray(repurchasePrizeList) && repurchasePrizeList.length > 0
    ? repurchasePrizeList
    : [initPrizeItem];

  const updateRepurchasePrize = (data: any) => {
    // 如果data是数组，直接使用；如果是对象，则更新第一个元素
    let newList;
    if (Array.isArray(data)) {
      newList = data;
    } else {
      newList = [{ ...prizeList[0], ...data }];
    }
    dispatch({ type: 'UPDATE_REPURCHASE_PRIZE_LIST', payload: newList });
    dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.REPURCHASE_PRIZE_LIST });
  };

    // 更新奖品列表并通知父组件
    const updatePrizeList = (newList: any[]) => {
        updateRepurchasePrize(newList);
        // onPrizeChange?.(newList);
    };

    // 添加复购奖品
    const addRepurchasePrize = () => {
        if (prizeList.length >= 20) {
            Message.warning('最多只能添加20个复购奖品');
            return;
        }

        const newPrize = {
            ...initPrizeItem,
            sortId: prizeList.length + 1,
        };

        const newList = [...prizeList, newPrize];
        updateRepurchasePrize(newList);
        Message.success('添加复购奖品成功');
    };

    // 编辑奖品
    const editPrizeHandler = async (index: number, record: any) => {
        console.log('编辑奖品', record);

        // 确保record有有效数据，如果没有则使用当前奖品数据
        const currentRecord = record || prizeList[index] || initPrizeItem;

        // 字段名映射：将表格中的字段名映射为编辑组件期望的字段名
        const mappedRecord = {
            ...currentRecord,
        };

        const isThanks = mappedRecord.lotteryType === PrizeTypeEnum.THANKS.value;
        const isPhysical = mappedRecord.lotteryType === PrizeTypeEnum.PRACTICALITY.value;
        const needBackStock = currentRecord.prizeId && isPhysical;
        const isNewPrize = currentRecord.isAddNew === true || !currentRecord.prizeId; // 新添加的奖品或没有prizeId的奖品

        // 封装编辑奖品的核心逻辑
        const handleEdit = async () => {
            try {
                let result: any;
                // 只有新添加的奖品才调用choosePrize让用户选择奖品
                if (isNewPrize && (!currentRecord.lotteryName || currentRecord.lotteryName === '')) {
                    result = await choosePrize({
                        activityType: 'prizeAndSku',
                        disabledTabs: [6, 7, 9],
                        status: [activityStatus, operationType],
                        needDisable: false,
                        needShowImg: true,
                    });
                } else {
                    result = await editPrize({
                        editPrizeInfo: mappedRecord,
                        disabledTabs: [6, 7, 9],
                        status: [activityStatus, operationType],
                        field: null, // 如果需要表单字段验证，可以传入field
                        activityType: 'prizeAndSku',
                        needShowImg: true,
                    });
                }

                if (result) {
                    console.log('编辑奖品结果：', result);
                    // 将编辑结果的字段名映射回表格期望的字段名
                    const mappedResult = {
                        ...currentRecord, // 保留原有数据
                        lotteryName: result.lotteryName || currentRecord.lotteryName,
                        lotteryType: result.lotteryType || currentRecord.lotteryType,
                        lotteryValue: result.lotteryValue || currentRecord.lotteryValue,
                        prizeNum: result.prizeNum || currentRecord.prizeNum,
                        dayLimitType: result.dayLimitType || currentRecord.dayLimitType,
                        awardLimitType: result.awardLimitType || currentRecord.awardLimitType,
                        awardLimitCount: result.awardLimitCount || currentRecord.awardLimitCount,
                        showImage: result.showImage || currentRecord.showImage,
                        price: result.price || currentRecord.price,
                        sectionNum: currentRecord.sectionNum,
                        goodsLine: currentRecord.goodsLine,
                        isAddNew: false, // 编辑后标记为非新增
                    };

                    const newList = [...prizeList];
                    newList[index] = mappedResult;
                    updateRepurchasePrize(newList);
                    Message.success('编辑奖品成功');
                }
            } catch (error) {
                console.error('编辑奖品失败：', error);
                Message.error(error.message);
            }
        };

        // 针对已开始活动且有ID的奖品且不是谢谢惠顾类型，需要先确认
        if (needBackStock) {
            Dialog.confirm({
                title: '提示',
                content: <WarningContent />,
                onOk: async () => {
                    // 先将列表中对应奖品重置为空
                    const newList = [...prizeList];
                    newList[index] = { ...initPrizeItem };
                    updatePrizeList(newList);
                    // 回滚库存
                    await handleBackStock(currentRecord);
                    // 然后执行编辑逻辑
                    await handleEdit();
                },
            });
        } else {
            // 其他情况直接编辑
            await handleEdit();
        }
    };

    const handleBackStock = async (record: any) => {
        try {
            // 只有实物奖品且有prizeId时才需要释放库存
            if (record.lotteryType === PrizeTypeEnum.PRACTICALITY.value && record.prizeId) {
                await activityCustomizeActivityMinusLotteryNum({
                    lotteryId: record.prizeId,
                    num: record.prizeNum,
                });
            }
        } catch (error) {
            console.error('回撤库存失败：', error);
            throw error; // 重新抛出错误，让调用方知道释放库存失败
        }
    };

    // 重置奖品为空
    const removePrize = async (index: number, record: any) => {
        const hasId = record.prizeId || record.prizeKey;

        // 封装删除奖品的核心逻辑
        const handleRemove = async () => {
            try {
                // 如果奖品有ID（已保存过的奖品），需要调用释放库存接口
                if (hasId) {
                    await handleBackStock(record);
                }
                // 如果只有一个奖品，不允许删除
                if (prizeList.length <= 1) {
                    Message.warning('至少需要保留一个复购奖品');
                    return;
                }

                const newList = [...prizeList];
                // 真正删除奖品：从数组中移除该项
                newList.splice(index, 1);
                // 重新调整剩余奖品的sortId，确保连续性
                const reorderedList = newList.map((prize, idx) => ({
                    ...prize,
                    sortId: idx + 1,
                }));
                updateRepurchasePrize(reorderedList);
                Message.success('奖品删除成功');
            } catch (error) {
                console.error('删除奖品失败：', error);
                Message.error('删除奖品失败');
            }
        };

        // 如果奖品有ID，需要提醒用户删除将释放已冻结库存
        const content = hasId
            ? '删除奖品将释放已冻结库存，请务必重新完整保存活动，是否继续？'
            : '确定要将此奖品删除吗？此次操作不可恢复';

        Dialog.confirm({
            title: '删除奖品',
            content,
            onOk: handleRemove,
        });
    };

    // 渲染操作按钮
    const renderAction = (_: boolean, index: number, record: any) => {
        const isCreate = operationType === 'add';
        const isCopy = operationType === 'copy';
        // 活动未开始
        const isNoStart = activityStatus === ACTIVITY_STATUS.NOT_STARTED;

        // 编辑按钮显示逻辑：活动进行中编辑且不是谢谢参与时展示 或 活动创建/复制/编辑新添加的奖品时展示
        const showEditButton = isCreate || isCopy || isEdit;

        // 其他按钮显示逻辑：仅在活动创建、复制和编辑未开始活动时展示
        const showOtherButton = isCreate || isCopy || isNoStart;

        return (
          <Box direction="row" align="center" spacing={10}>
            {showEditButton && (
            <Button type={'primary'} text onClick={() => editPrizeHandler(index, record)}>编辑</Button>
                )}
            {showOtherButton && (
            <Box direction="row" align="center" spacing={10}>
              <Button
                type={'primary'}
                text
                onClick={() => removePrize(index, record)}
                disabled={prizeList.length <= 1} // 至少保留一个奖品
              >
                删除
              </Button>
            </Box>
                )}
          </Box>
        );
    };

  return (
    <Container title="复购奖品设置">
      <Form labelAlign="left" style={{ marginTop: '15px' }}>
        <FormItem label="复购奖品列表" required>
          <Box direction="row" justify="space-between" align="center" style={{ marginBottom: '10px' }}>
            <div style={{ color: '#989898' }}>当前已添加 {prizeList.length} 个复购奖品（最多20个）</div>
            {(!isEnded && !isView) && (
              <Button
                type="primary"
                onClick={addRepurchasePrize}
                disabled={prizeList.length >= 20}
              >
                添加复购奖品
              </Button>
            )}
          </Box>
          <Table dataSource={prizeList}>
            <Table.Column title="奖项" cell={(_, index) => index + 1} />
            <Table.Column
              title="段数"
              dataIndex="sectionNum"
              cell={(value, index, record) => {
              return (
                <NumberPicker
                  min={1}
                  max={5}
                  value={record.sectionNum}
                  onChange={(value) => {
                    const newList = [...prizeList];
                    newList[index] = { ...record, sectionNum: value };
                    updateRepurchasePrize(newList);
                  }}
                />
              );
              }}
            />
            <Table.Column
              title="品线"
              dataIndex="goodsLine"
              cell={(value, index, record) => {
              return (
                <Input
                  value={record.goodsLine}
                  placeholder="请输入品线名称"
                  maxLength={20}
                  onChange={(value) => {
                    const newList = [...prizeList];
                    newList[index] = { ...record, goodsLine: value };
                    updateRepurchasePrize(newList);
                  }}
                />
              );
            }}
            />
            <Table.Column title="奖品名称" dataIndex="lotteryName" cell={(value) => value || '-'} />
            <Table.Column
              title="奖品类型"
              dataIndex="lotteryType"
              cell={(value) => (value ? getPrizeTypeLabel(value) : '-')}
            />
            <Table.Column
              title="单位数量"
              cell={(value, index, record) => {
                  if (!record.lotteryType) return '-';
                  if (record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value) {
                    return record.lotteryValue || '-';
                  } else {
                    return 1;
                  }
                }}
            />
            <Table.Column title="发放份数" dataIndex="prizeNum" cell={value => value || '-'} />
            <Table.Column
              title="奖品图"
              dataIndex="showImage"
              cell={(value) => {
                  if (value) {
                    return <img src={value} alt="奖品" style={{ width: 50, height: 50, objectFit: 'cover' }} />;
                  } else {
                    return '-';
                  }
                }}
            />
            {
                (!isEnded && !isView) && <Table.Column title="操作" cell={renderAction} />
            }
          </Table>
        </FormItem>
        {/* <FormItem label="奖品延迟发放" required disabled> */}
        {/*  <FormItem disabled> */}
        {/*    <Radio.Group */}
        {/*      value={formData.isDelayedDisttribution} */}
        {/*      onChange={(isDelayedDisttribution) => */}
        {/*                  setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution })} */}
        {/*    > */}
        {/*      <Radio value={0}>否</Radio> */}
        {/*      <Radio value={1}>是</Radio> */}
        {/*    </Radio.Group> */}
        {/*  </FormItem> */}
        {/*  {formData.isDelayedDisttribution === 1 && ( */}
        {/*  <FormItem required requiredMessage="请输入延迟天数" disabled> */}
        {/*    延迟发放{' '} */}
        {/*    <NumberPicker */}
        {/*      min={1} */}
        {/*      max={7} */}
        {/*      type="inline" */}
        {/*      value={formData.awardDays} */}
        {/*      onChange={(awardDays: number) => setData({ awardDays })} */}
        {/*    />{' '} */}
        {/*    天 */}
        {/*  </FormItem> */}
        {/*      )} */}
        {/* </FormItem> */}
      </Form>
    </Container>
  );
}

