import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateThreshold } from './validator';
import { ModuleType, ThresholdState } from '@/pages/activity/10202/create/type';


/**
 * 门槛模块的自定义Hook
 * 提供对门槛状态的访问和更新方法
 */
export function useThreshold() {
  const { state, dispatch } = useActivity();

  // 更新门槛并自动重新验证
  const updateThreshold = (data?: Partial<ThresholdState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_THRESHOLD',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      threshold: {
        ...state.threshold,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validateThreshold(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.THRESHOLD,
      payload: errors,
    });
  };

  return {
    threshold: state.threshold,
    errors: state.errors[ModuleType.THRESHOLD] || [],
    updateThreshold,
    dispatch,
  };
}
