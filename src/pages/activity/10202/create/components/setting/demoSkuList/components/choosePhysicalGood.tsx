import { Dialog } from '@alifd/next';
import ChoosePhysicalGoodDialogContent from './ChoosePhysicalGoodDialogContent';
import { getPrizeTypeLabel } from '@/utils';
import { addDialogRef } from '@/utils/dialogMapper';

export default function choosePhysicalGood({ record, prizeType, field, activityType, status }: any) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: `编辑${getPrizeTypeLabel(prizeType)}`,
      width: 600,
      centered: true,
      closeMode: record.prizeId ? [] : ['close'],
      content: (
        <ChoosePhysicalGoodDialogContent
          record={record}
          prizeType={prizeType}
          field={field}
          activityType={activityType}
          status={status}
          onResolve={result => {
            resolve(result); // result为表单数据
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}