import { Box, Button, Card, Dialog, Field, Form, Icon, Input, Message, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataGetPrizeInfo, dataGetPrizeLog, addLotteryNum } from '@/api/v10202';
import NumberInput from '@/components/NumberInput/NumberInput';
import { PrizeTypeEnum } from '@/utils';


export default function PrizeData({ activityId }) {
  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  const [addVisible, setAddVisible] = useState(false);
  const [recordVisible, setRecordVisible] = useState(false);
  const [addNum, setAddNum] = useState<number>(1);
  const addField = Field.useField();
  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 16 },
  };

  const [prizeInfo, setPrizeInfo] = useState<any>(null);

  const [recordTable, setRecordTable] = useState<any[]>([]);


  const fetchData = async () => {
    setLoading(true);
    try {
      const data = await dataGetPrizeInfo({ activityId } as any);

      setTableData(data || []);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const noStock = () => {
    return prizeInfo?.lotteryType === PrizeTypeEnum.COUPON.value ||
      prizeInfo?.lotteryType === PrizeTypeEnum.MEMBER_POINT.value;
  };
  const handleAddLotteryNum = (record) => {
    setAddVisible(true);
    setPrizeInfo(record);
  };

  const handleAddLotteryLog = async (id: string) => {
    const data = await dataGetPrizeLog({ id } as any);
    setRecordTable(data);
    setRecordVisible(true);
  };

  useEffect(() => {
    fetchData().then();
  }, []);


  return (
    <div>
      <Card
        title="活动参与数据"
        subTitle={
          <Box
            style={{ display: 'inline-flex' }}
            direction="row"
            align="center"
            spacing={10}
          >
            <span style={{ color: 'var(--color-text-regular)' }}>统计截止：{dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss') || '--'}</span>
            <Button
              type="primary"
              iconSize={'small'}
              text
              style={{ zoom: 0.7 }}
              onClick={() => fetchData()}
            >
              <Icon type="refresh" />
            </Button>
          </Box>
      }
        contentHeight={'auto'}
        showTitleBullet={false}
        showHeadDivider={false}
        style={{ flexShrink: 0, border: 'none', marginTop: 16 }}
      >
        <Table
          dataSource={tableData}
          loading={loading}
        >
          <Table.Column title="奖品名称" dataIndex="lotteryName" />
          <Table.Column title="奖品类型" dataIndex="lotteryTypeStr" />
          <Table.Column title="发放总量" dataIndex="prizeNum" />
          <Table.Column title="已发放数" dataIndex="sendPrizeNum" />
          <Table.Column title="剩余库存" dataIndex="remainPrizeNum" />
          <Table.Column
            title="操作"
            dataIndex="id"
            cell={(value, index, record) => {
            return (<Box direction="row" spacing={10} align="center">
              <Button text type="primary" onClick={() => handleAddLotteryNum(record)}>补充奖品</Button>
              <Button text type="primary" onClick={() => handleAddLotteryLog(value)}>奖品追加记录</Button>
            </Box>);
          }}
          />
        </Table>

      </Card>
      <Dialog
        visible={addVisible}
        v2
        title="追加奖品"
        footerAlign="center"
        onOk={() => {
        addField.validate((errors) => {
          if (errors) return;
          addLotteryNum({
            id: prizeInfo.id,
            addNum,
          }).then(() => {
            setAddVisible(false);
            Message.success('追加奖品成功');
            fetchData().then();
          }).catch(e => {
            Message.error(e.message);
          });
        });
      }}
        onCancel={() => setAddVisible(false)}
        onClose={() => setAddVisible(false)}
      >
        <Form field={addField} {...layout} labelAlign="left">
          <Form.Item label="实物奖品名称">
            <Input disabled value={prizeInfo?.lotteryName} />
          </Form.Item>
          <Form.Item label="当前可用库存" x-if={prizeInfo?.lotteryType === PrizeTypeEnum.PRACTICALITY.value}>
            <Input disabled value={prizeInfo?.remainLotteryPrizeNum} />
          </Form.Item>
          <Form.Item label="追加库存数" required requiredMessage="请输入追加库存数">
            <NumberInput
              min={1}
              max={noStock() ? 9999 : prizeInfo?.remainLotteryPrizeNum}
              value={addNum}
              onChange={(value: number) => setAddNum(value)}
            />
          </Form.Item>
        </Form>
      </Dialog>
      <Dialog title="奖品追加记录" visible={recordVisible} v2 footer={false} onClose={() => setRecordVisible(false)}>
        <Table dataSource={recordTable}>
          <Table.Column title="时间" dataIndex="createTime" cell={(value) => dayjs(value).format('YYYY-MM-DD HH:mm:ss')} />
          <Table.Column title="追加账号" dataIndex="operator" />
          <Table.Column title="追加前发放总量" dataIndex="oldNum" />
          <Table.Column title="追加数量" dataIndex="addNum" />
          <Table.Column title="追加后发放总量" dataIndex="newNum" />
        </Table>
        <Box direction="row" justify="center" margin={[16, 0, 0, 0]}>
          <Button type="primary" onClick={() => setRecordVisible(false)}>我知道了</Button>
        </Box>
      </Dialog>
    </div>
  );
}
