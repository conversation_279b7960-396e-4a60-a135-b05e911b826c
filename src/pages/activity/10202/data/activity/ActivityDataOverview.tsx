import { useState, useEffect, useRef } from 'react';
import { FunnelChart } from 'bizcharts';
import { Box, Button, Card, Icon, ResponsiveGrid, Message, Loading } from '@alifd/next';
import DataCard from '@/components/DataCard/DataCard';
import { dataGetTotalData } from '@/api/v10202';
import dayjs from 'dayjs';

export default function ActivityDataOverview({ activityId, startTime, endTime }) {
  const [activityData, setActivityData] = useState<any>(null);
  const chartRef: any = useRef(null);
  const [loading, setLoading] = useState(false);

  // 转换接口返回的数组数据为对象格式
  const transformArrayToObject = (dataArray: any[]) => {
    const transformedData: any = {};

    if (!Array.isArray(dataArray)) {
      return transformedData;
    }

    dataArray.forEach((item: any) => {
      if (item.key && item.value !== undefined) {
        transformedData[item.key] = +item.value;
      }
    });

    return transformedData;
  };

  useEffect(() => {
    if (activityId) {
      fetchData().then();
    }
  }, [activityId]);

  useEffect(() => {
    return () => {
      if (chartRef.current) {
        chartRef.current = null;
      }
    };
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await dataGetTotalData({
        activityId,
        queryType: '1,2,3,4,5,6,7,8,9,10,11,12,13,14',
        startTime,
        endTime,
      } as any);
      console.log(res, ' res');

      // 使用转换方法处理返回的数据
      const transformedData = transformArrayToObject(res);
      setActivityData(transformedData);
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  if (!activityData) return null;

  return (
    <Loading visible={loading} style={{ width: '100%', height: '100%' }}>
      <Box
        direction="row"
        align="center"
        spacing={16}
        style={{ overflow: 'auto' }}
      >
        <Card
          title="活动参与数据"
          subTitle={
            <Box
              style={{ display: 'inline-flex' }}
              direction="row"
              align="center"
              spacing={10}
            >
              <span style={{ color: 'var(--color-text-regular)' }}>统计截止：{dayjs().format('YYYY-MM-DD HH:mm:ss') || '--'}</span>
              <Button
                type="primary"
                iconSize={'small'}
                text
                style={{ zoom: 0.7 }}
                onClick={() => fetchData()}
              >
                <Icon type="refresh" />
              </Button>
            </Box>
          }
          contentHeight={'auto'}
          showTitleBullet={false}
          showHeadDivider={false}
          style={{ flexShrink: 0 }}
        >
          <Box
            direction="row"
            align="center"
            spacing={16}
          >
            <Box spacing={10}>
              <div>
                <DataCard
                  title="PV"
                  tooltip="时间窗内，访问用户的次数"
                  value={activityData?.pv}
                />
              </div>
              <div>
                <DataCard
                  title="UV"
                  tooltip="时间窗内，访问用户的数量（活动全程去重）"
                  value={activityData?.uv}
                />
              </div>
              <div>
                <DataCard
                  title="活动入会人数"
                  tooltip="时间窗内，通过活动入会的人数（活动全程去重）"
                  value={activityData?.joinMember}
                />
              </div>
              <div>
                <DataCard
                  title="参与人数"
                  tooltip="时间窗内，活动报名人数（活动全程去重）"
                  value={activityData?.participate}
                />
              </div>
            </Box>
            <FunnelChart
              onGetG2Instance={g2 => {
                chartRef.current = g2.chart;
              }}
              width={400}
              data={[
                { action: 'PV', num: activityData?.pv || 0 },
                { action: 'UV', num: activityData?.uv || 0 },
                { action: '活动入会人数', num: activityData?.joinMember || 0 },
                { action: '参与人数', num: activityData?.participate || 0 },
              ]}
              xField="action"
              yField="num"
              conversionTag={false}
              label={{
                position: 'right',
                offsetX: 10,
                style: { fill: '#333', fontSize: 12 },
              }}
              tooltip={{
                showMarkers: false,
              }}
            />
          </Box>
        </Card>

        <DataCard
          title="下单转化率"
          tooltip="（活动下单人数 / 活动参与人数）*100%"
          value={activityData?.buyRate}
          warning
          connectingLine
        />

        <Card
          title="购物订单数据"
          contentHeight={'auto'}
          showTitleBullet={false}
          showHeadDivider={false}
          style={{ flexShrink: 0 }}
        >
          <ResponsiveGrid gap={10}>
            <ResponsiveGrid.Cell
              colSpan={8}
              rowSpan={3}
            >
              <Box
                className="order-num-cell"
                justify="space-between"
              >
                <DataCard
                  title="活动下单人数"
                  tooltip={'（1）【玩法设置】中所设【下单时间】内有下单的买家总数\n（2）若勾选【仅统计报名后订单】，则仅统计报名后有下单的买家总数'}
                  value={activityData?.uvBuyer}
                />
                <Box
                  direction="row"
                  justify="space-between"
                >
                  <DataCard
                    title="下单新客人数"
                    tooltip={'【玩法设置】中所设【下单时间】之前无店铺购买记录，【下单时间】内有购买商品的买家数'}
                    value={activityData?.newBuyer}
                  />
                  <DataCard
                    title="下单老客人数"
                    tooltip={'【玩法设置】中所设【下单时间】之前有店铺购买记录，【下单时间】内也有购买商品的买家数'}
                    value={activityData?.oldBuyer}
                  />
                </Box>
              </Box>
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动下单数"
                tooltip={'（1）【玩法设置】中所设下单时间内的有效订单总数（排除退款）\n（2）若勾选【仅统计报名后订单】，则仅统计报名的有效订单数'}
                value={activityData?.uvBuyItem}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动下单金额"
                tooltip={'有效订单总金额(排除退款)'}
                value={activityData?.uvBuyAmt}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动下单件数"
                tooltip={'有效订单包含的有效商品件数'}
                value={activityData?.uvBuyItem}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动FREQ"
                tooltip={'活动下单数/活动下单人数'}
                value={activityData?.actFREQ}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动Spending"
                tooltip={'活动下单金额/活动下单人数'}
                value={activityData?.actSpending}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动AUS"
                tooltip={'活动下单金额/活动下单数'}
                value={activityData?.actAus}
              />
            </ResponsiveGrid.Cell>
          </ResponsiveGrid>
        </Card>
      </Box>
    </Loading>

  );
}
