import Container from '@/components/Container';
import usePagination from '@/hooks/usePagination';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import { Balloon, Box, Button, DatePicker2, Input, Message, Pagination, Select, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataGetDrawRecord, dataGetDrawRecordExport } from '@/api/v10202';
import { Auth } from '@/components/Auth';

const pageNum = 1;
const pageSize = 10;

export default function ReceiveData({ activityId }) {
  const defaultSearchParams = {
    activityId,
    openId: '',
    timeRange: [],
    nick: '',
    drawStatus: -1, // 中奖状态：-1全部, 1已中奖, 2未中奖
    receiveStatus: -1, // 领取状态：-1全部, 1已领取, 2未领取
    startTime: '',
    endTime: '',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
    activityId,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);
const [loading, setLoading] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};


const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
  await fetchData({
    pageNum: current,
    pageSize: size,
    ...params,
  });
});

const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];

    // 构建查询参数
    const queryParams = {
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const { records, total }: any = await dataGetDrawRecord({
      ...queryParams,
      ...params,
    });

    setTableData(records || []);
    pagination.setTotal(total || 0);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleExport = async () => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];
    const data: any = await dataGetDrawRecordExport({
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
    downloadExcel(data, `抽奖记录${dayjs().format('YYYY-MM-DD HH:mm')}`);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  pagination.changePage(pageNum);
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  pagination.reset(defaultSearchParams);
};

useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Container style={{ marginTop: 16 }}>
      <Box direction="row" spacing={16} margin={[0, 0, 16, 0]} wrap align="center">
        <Input label={'用户昵称'} value={searchParams.nick} onChange={(value) => updateSearchParam('nick', value)} />
        <Input label={'openId'} value={searchParams.openId} onChange={(value) => updateSearchParam('openId', value)} />
        <DatePicker2.RangePicker
          type={'range'}
          label={'抽奖时间'}
          value={searchParams.timeRange}
          onChange={(value) => updateSearchParam('timeRange', value)}
        />
        <Select label={'中奖状态'} value={searchParams.drawStatus} onChange={(value) => updateSearchParam('drawStatus', value)}>
          <Select.Option value={-1}>全部</Select.Option>
          <Select.Option value={1}>已中奖</Select.Option>
          <Select.Option value={2}>未中奖</Select.Option>
        </Select>
        <Select label={'领取状态'} value={searchParams.receiveStatus} onChange={(value) => updateSearchParam('receiveStatus', value)}>
          <Select.Option value={-1}>全部</Select.Option>
          <Select.Option value={1}>已领取</Select.Option>
          <Select.Option value={2}>未领取</Select.Option>
        </Select>
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
        <Auth authKey={'activity_list_data_export'} >
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1)}</div>;
        }}
        />
        <Table.Column
          title="openId"
          dataIndex="openId"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
        }}
        />
        <Table.Column title="抽奖时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column
          title="中奖状态"
          dataIndex="statusStr"
          cell={(value) => {
          return <div className={value === '已中奖' ? 'status-normal' : ''}>{value}</div>;
        }}
        />
        <Table.Column title="奖品类型" dataIndex="prizeTypeStr" />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column
          title="领取状态"
          dataIndex="sendStatusStr"
          cell={(value) => {
          return <div className={value === '领取成功' ? 'status-normal' : ''}>{value}</div>;
        }}
        />
        <Table.Column
          width={200}
          title="备注"
          dataIndex="missReason"
          cell={(value) => {
            if (!value) return <div>-</div>;
            return (
              <Balloon
                trigger={
                  <div
                    style={{
                      maxWidth: '180px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      cursor: 'pointer',
                    }}
                  >
                    {value}
                  </div>
                }
                closable={false}
                triggerType="hover"
              >
                <div style={{ maxWidth: '300px', wordBreak: 'break-all' }}>{value}</div>
              </Balloon>
            );
          }}
        />
      </Table>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={(total) => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Container>
  );
}
