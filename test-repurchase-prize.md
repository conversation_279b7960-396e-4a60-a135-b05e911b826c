# 复购奖品编辑功能修复总结

## 问题描述
点击复购奖品的编辑按钮，打印：编辑奖品 []，但是没有弹出奖品选择组件

## 问题根因分析
1. **数据结构问题**：`repurchasePrizeList` 被定义为数组，但在 `updateRepurchasePrize` 函数中被当作单个对象处理
2. **Table数据源问题**：Table的dataSource被设置为 `[repurchasePrizeList]`，导致数据结构错误
3. **编辑逻辑问题**：编辑函数中的record参数为空或结构不正确

## 修复内容

### 1. 修复初始数据结构
```javascript
const initPrizeItem = {
    lotteryName: '谢谢参与',
    lotteryType: PrizeTypeEnum.THANKS.value,
    lotteryValue: '',
    prizeNum: 1,
    dayLimitType: 2,
    awardLimitType: 2,
    awardLimitCount: 1,
    sectionNum: 1,
    goodsLine: '',
    showImage: '',
    price: '0',
    isAddNew: true,
};
```

### 2. 修复数据处理逻辑
```javascript
// 确保复购奖品列表至少有一个初始项
const prizeList = Array.isArray(repurchasePrizeList) && repurchasePrizeList.length > 0 
  ? repurchasePrizeList 
  : [initPrizeItem];

const updateRepurchasePrize = (data: any) => {
  // 如果data是数组，直接使用；如果是对象，则更新第一个元素
  let newList;
  if (Array.isArray(data)) {
    newList = data;
  } else {
    newList = [{ ...prizeList[0], ...data }];
  }
  dispatch({ type: 'UPDATE_REPURCHASE_PRIZE_LIST', payload: newList });
  dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.REPURCHASE_PRIZE_LIST });
};
```

### 3. 修复编辑逻辑
```javascript
const editPrizeHandler = async (index: number, record: any) => {
    console.log('编辑奖品', record);

    // 确保record有有效数据，如果没有则使用当前奖品数据
    const currentRecord = record || prizeList[index] || initPrizeItem;
    
    // 字段名映射：将表格中的字段名映射为编辑组件期望的字段名
    const mappedRecord = {
        ...currentRecord,
    };

    const isNewPrize = currentRecord.isAddNew === true || !currentRecord.prizeId;
    
    // 编辑逻辑...
};
```

### 4. 修复Table数据源
```javascript
<Table dataSource={prizeList}>
  {/* 表格列定义 */}
</Table>
```

### 5. 修复表格内联编辑
```javascript
<Table.Column
  title="段数"
  dataIndex="sectionNum"
  cell={(value, index, record) => {
    return (
      <NumberPicker
        value={record.sectionNum}
        onChange={(value) => {
          const newList = [...prizeList];
          newList[index] = { ...record, sectionNum: value };
          updateRepurchasePrize(newList);
        }}
      />
    );
  }}
/>
```

## 修复效果
1. ✅ 修复了编辑按钮点击时record为空的问题
2. ✅ 修复了Table数据源配置错误的问题
3. ✅ 修复了数据更新逻辑错误的问题
4. ✅ 确保了复购奖品列表始终有有效的数据结构
5. ✅ 修复了表格内联编辑功能

## 测试建议
1. 点击复购奖品的编辑按钮，应该能正常弹出奖品选择组件
2. 编辑奖品后，数据应该正确保存到列表中
3. 表格中的段数和品线字段应该能正常编辑
4. 删除奖品后，如果列表为空，应该自动添加一个初始项

## 注意事项
- 修复后的代码保持了向后兼容性
- 添加了数据验证和错误处理
- 确保了数据结构的一致性
