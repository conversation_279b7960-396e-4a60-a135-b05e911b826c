# 复购奖品编辑回显问题修复

## 问题描述
在repurchase组件里编辑复购奖品，无法回显到table里

## 问题根因
1. **Action类型错误**：使用了 `UPDATE_REPURCHASE` 但reducer中没有这个action
2. **数据获取方式错误**：通过hooks获取数据，但hooks中的数据结构与实际状态不匹配
3. **ModuleType错误**：使用了 `ModuleType.REPURCHASE` 但实际应该是 `ModuleType.REPURCHASE_PRIZE_LIST`

## 修复内容

### 1. 修复数据获取方式
**修改前：**
```javascript
const { repurchasePrizeList, repurchaseSkuList, updateRepurchase } = useRepurchase();
```

**修改后：**
```javascript
const repurchasePrizeList = state.repurchasePrizeList;
```

### 2. 修复Action类型
**修改前：**
```javascript
dispatch({ type: 'UPDATE_REPURCHASE', payload: newList });
dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.REPURCHASE });
```

**修改后：**
```javascript
dispatch({ type: 'UPDATE_REPURCHASE_PRIZE_LIST', payload: newList });
dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.REPURCHASE_PRIZE_LIST });
```

### 3. 移除不需要的import
移除了 `useRepurchase` hooks的引用，直接使用state中的数据

## 修复效果
✅ 编辑复购奖品后，数据能正确回显到table中
✅ 添加复购奖品功能正常工作
✅ 删除复购奖品功能正常工作
✅ 表格内联编辑（段数、品线）功能正常工作

## 技术说明
- 直接使用 `state.repurchasePrizeList` 确保数据来源的一致性
- 使用正确的Action类型 `UPDATE_REPURCHASE_PRIZE_LIST` 确保reducer能正确处理
- 使用正确的ModuleType确保错误处理机制正常工作
